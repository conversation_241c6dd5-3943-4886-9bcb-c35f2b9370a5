<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Country;
use Illuminate\Support\Facades\Auth;

class CountryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $countries = Country::with(['creator', 'updater'])->latest()->paginate(10);
        return view('backend.countries.index', compact('countries'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.countries.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'code' => 'required|string|max:3|unique:countries,code',
            'name' => 'required|string|max:255',
            'serial_id' => 'nullable|integer',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'phonecode' => 'nullable|string|max:10',
            'timezone_id' => 'nullable|string|max:255',
            'currency_name' => 'nullable|string|max:255',
            'currency_code' => 'nullable|string|max:3',
            'currency_symbol' => 'nullable|string|max:10',
            'is_active' => 'required|boolean'
        ]);

        $data = $request->all();
        $data['created_by'] = Auth::id();
        $data['updated_by'] = Auth::id();

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads/countries'), $filename);
            $data['image'] = $filename;
        }

        Country::create($data);

        return redirect()->route('countries.index')
                        ->with('success', 'Country created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $country = Country::with(['creator', 'updater', 'states'])->findOrFail($id);
        return view('backend.countries.show', compact('country'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $country = Country::findOrFail($id);
        return view('backend.countries.edit', compact('country'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $country = Country::findOrFail($id);

        $request->validate([
            'code' => 'required|string|max:3|unique:countries,code,' . $id . ',country_id',
            'name' => 'required|string|max:255',
            'serial_id' => 'nullable|integer',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'phonecode' => 'nullable|string|max:10',
            'timezone_id' => 'nullable|string|max:255',
            'currency_name' => 'nullable|string|max:255',
            'currency_code' => 'nullable|string|max:3',
            'currency_symbol' => 'nullable|string|max:10',
            'is_active' => 'required|boolean'
        ]);

        $data = $request->all();
        $data['updated_by'] = Auth::id();

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($country->image && file_exists(public_path('uploads/countries/' . $country->image))) {
                unlink(public_path('uploads/countries/' . $country->image));
            }

            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads/countries'), $filename);
            $data['image'] = $filename;
        }

        $country->update($data);

        return redirect()->route('countries.index')
                        ->with('success', 'Country updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $country = Country::findOrFail($id);

        // Delete image if exists
        if ($country->image && file_exists(public_path('uploads/countries/' . $country->image))) {
            unlink(public_path('uploads/countries/' . $country->image));
        }

        $country->delete();

        return redirect()->route('countries.index')
                        ->with('success', 'Country deleted successfully.');
    }
}
