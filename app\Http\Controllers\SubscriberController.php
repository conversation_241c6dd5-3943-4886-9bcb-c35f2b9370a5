<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Subscriber;
use App\Plan;
use App\Country;
use App\State;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class SubscriberController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $subscribers = Subscriber::with(['creator', 'updater', 'plan', 'country', 'state'])
                                ->latest()
                                ->paginate(10);
        return view('backend.subscribers.index', compact('subscribers'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $plans = Plan::active()->orderBy('name')->get();
        $countries = Country::active()->orderBy('name')->get();
        $states = State::active()->orderBy('name')->get();

        return view('backend.subscribers.create', compact('plans', 'countries', 'states'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'subscriber_name' => 'required|string|max:255',
            'email_id' => 'required|email|unique:subscribers,email_id',
            'phone_no' => 'required|string|max:20',
            'start_date' => 'required|date',
            'expiry_date' => 'required|date|after:start_date',
            'plan_id' => 'required|exists:plans,id',
            'country_id' => 'required|exists:countries,country_id',
            'state_id' => 'required|exists:states,state_id',
            'amount_paid' => 'required|numeric|min:0',
            'status' => 'required|in:active,inactive,expired,suspended'
        ]);

        $data = $request->all();
        $data['created_by'] = Auth::id();
        $data['updated_by'] = Auth::id();

        Subscriber::create($data);

        return redirect()->route('subscribers.index')
                        ->with('success', 'Subscriber created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $subscriber = Subscriber::with(['creator', 'updater', 'plan', 'country', 'state'])
                                ->findOrFail($id);
        return view('backend.subscribers.show', compact('subscriber'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $subscriber = Subscriber::findOrFail($id);
        $plans = Plan::active()->orderBy('name')->get();
        $countries = Country::active()->orderBy('name')->get();
        $states = State::active()->orderBy('name')->get();

        return view('backend.subscribers.edit', compact('subscriber', 'plans', 'countries', 'states'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $subscriber = Subscriber::findOrFail($id);

        $request->validate([
            'subscriber_name' => 'required|string|max:255',
            'email_id' => 'required|email|unique:subscribers,email_id,' . $id,
            'phone_no' => 'required|string|max:20',
            'start_date' => 'required|date',
            'expiry_date' => 'required|date|after:start_date',
            'plan_id' => 'required|exists:plans,id',
            'country_id' => 'required|exists:countries,country_id',
            'state_id' => 'required|exists:states,state_id',
            'amount_paid' => 'required|numeric|min:0',
            'status' => 'required|in:active,inactive,expired,suspended'
        ]);

        $data = $request->all();
        $data['updated_by'] = Auth::id();

        $subscriber->update($data);

        return redirect()->route('subscribers.index')
                        ->with('success', 'Subscriber updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $subscriber = Subscriber::findOrFail($id);
        $subscriber->delete();

        return redirect()->route('subscribers.index')
                        ->with('success', 'Subscriber deleted successfully.');
    }

    /**
     * Get states by country (AJAX)
     */
    public function getStatesByCountry($countryId)
    {
        $states = State::where('country_id', $countryId)
                      ->where('is_active', true)
                      ->orderBy('name')
                      ->get(['state_id', 'name']);

        return response()->json($states);
    }
}
