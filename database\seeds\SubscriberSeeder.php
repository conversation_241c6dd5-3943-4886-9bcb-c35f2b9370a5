<?php

use Illuminate\Database\Seeder;
use App\Subscriber;
use App\Plan;
use App\Country;
use App\State;
use App\User;
use Carbon\Carbon;

class SubscriberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first user as creator
        $user = User::first();
        $userId = $user ? $user->id : null;

        // Get plans, countries, and states
        $basicPlan = Plan::where('name', 'Basic Plan')->first();
        $standardPlan = Plan::where('name', 'Standard Plan')->first();
        $premiumPlan = Plan::where('name', 'Premium Plan')->first();

        $india = Country::where('code', 'IN')->first();
        $usa = Country::where('code', 'US')->first();
        $uk = Country::where('code', 'GB')->first();

        $tamilNadu = State::where('name', 'Tamil Nadu')->first();
        $karnataka = State::where('name', 'Karnataka')->first();
        $california = State::where('name', 'California')->first();
        $newYork = State::where('name', 'New York')->first();
        $england = State::where('name', 'England')->first();

        $subscribers = [
            [
                'subscriber_name' => 'Rajesh Kumar',
                'email_id' => '<EMAIL>',
                'phone_no' => '+91-9876543210',
                'start_date' => Carbon::now()->subDays(30),
                'expiry_date' => Carbon::now()->addDays(335),
                'plan_id' => $basicPlan ? $basicPlan->id : 1,
                'country_id' => $india ? $india->country_id : 1,
                'state_id' => $tamilNadu ? $tamilNadu->state_id : 1,
                'amount_paid' => 99.99,
                'status' => 'active',
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
            [
                'subscriber_name' => 'John Smith',
                'email_id' => '<EMAIL>',
                'phone_no' => '******-123-4567',
                'start_date' => Carbon::now()->subDays(60),
                'expiry_date' => Carbon::now()->addDays(30),
                'plan_id' => $standardPlan ? $standardPlan->id : 2,
                'country_id' => $usa ? $usa->country_id : 2,
                'state_id' => $california ? $california->state_id : 6,
                'amount_paid' => 299.99,
                'status' => 'active',
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
            [
                'subscriber_name' => 'Sarah Johnson',
                'email_id' => '<EMAIL>',
                'phone_no' => '+44-20-7946-0958',
                'start_date' => Carbon::now()->subDays(180),
                'expiry_date' => Carbon::now()->addDays(185),
                'plan_id' => $premiumPlan ? $premiumPlan->id : 3,
                'country_id' => $uk ? $uk->country_id : 3,
                'state_id' => $england ? $england->state_id : 9,
                'amount_paid' => 599.99,
                'status' => 'active',
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
            [
                'subscriber_name' => 'Arjun Patel',
                'email_id' => '<EMAIL>',
                'phone_no' => '+91-9123456789',
                'start_date' => Carbon::now()->subDays(400),
                'expiry_date' => Carbon::now()->subDays(35),
                'plan_id' => $basicPlan ? $basicPlan->id : 1,
                'country_id' => $india ? $india->country_id : 1,
                'state_id' => $karnataka ? $karnataka->state_id : 2,
                'amount_paid' => 99.99,
                'status' => 'expired',
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
            [
                'subscriber_name' => 'Emily Davis',
                'email_id' => '<EMAIL>',
                'phone_no' => '******-987-6543',
                'start_date' => Carbon::now()->subDays(15),
                'expiry_date' => Carbon::now()->addDays(75),
                'plan_id' => $standardPlan ? $standardPlan->id : 2,
                'country_id' => $usa ? $usa->country_id : 2,
                'state_id' => $newYork ? $newYork->state_id : 7,
                'amount_paid' => 299.99,
                'status' => 'suspended',
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
        ];

        foreach ($subscribers as $subscriber) {
            Subscriber::create($subscriber);
        }
    }
}
