/*! Summernote v0.8.15 | (c) 2013- <PERSON> and other contributors | MIT license */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("jquery"));else if("function"==typeof define&&define.amd)define(["jquery"],e);else{var o="object"==typeof exports?e(require("jquery")):e(t.jQuery);for(var i in o)("object"==typeof exports?exports:t)[i]=o[i]}}(window,(function(t){return function(t){var e={};function o(i){if(e[i])return e[i].exports;var n=e[i]={i,l:!1,exports:{}};return t[i].call(n.exports,n,n.exports,o),n.l=!0,n.exports}return o.m=t,o.c=e,o.d=function(t,e,i){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(o.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)o.d(i,n,function(e){return t[e]}.bind(null,n));return i},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=50)}({0:function(e,o){e.exports=t},1:function(t,e,o){"use strict";var i=o(0),n=o.n(i);class s{constructor(t,e,o,i){this.markup=t,this.children=e,this.options=o,this.callback=i}render(t){const e=n()(this.markup);if(this.options&&this.options.contents&&e.html(this.options.contents),this.options&&this.options.className&&e.addClass(this.options.className),this.options&&this.options.data&&n.a.each(this.options.data,(t,o)=>{e.attr("data-"+t,o)}),this.options&&this.options.click&&e.on("click",this.options.click),this.children){const t=e.find(".note-children-container");this.children.forEach(o=>{o.render(t.length?t:e)})}return this.callback&&this.callback(e,this.options),this.options&&this.options.callback&&this.options.callback(e),t&&t.append(e),e}}e.a={create:(t,e)=>function(){const o="object"==typeof arguments[1]?arguments[1]:arguments[0];let i=Array.isArray(arguments[0])?arguments[0]:[];return o&&o.children&&(i=o.children),new s(t,i,o,e)}}},2:function(t,e){(function(e){t.exports=e}).call(this,{})},3:function(t,e,o){"use strict";var i=o(0),n=o.n(i);n.a.summernote=n.a.summernote||{lang:{}},n.a.extend(n.a.summernote.lang,{"en-US":{font:{bold:"Bold",italic:"Italic",underline:"Underline",clear:"Remove Font Style",height:"Line Height",name:"Font Family",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript",size:"Font Size",sizeunit:"Font Size Unit"},image:{image:"Picture",insert:"Insert Image",resizeFull:"Resize full",resizeHalf:"Resize half",resizeQuarter:"Resize quarter",resizeNone:"Original size",floatLeft:"Float Left",floatRight:"Float Right",floatNone:"Remove float",shapeRounded:"Shape: Rounded",shapeCircle:"Shape: Circle",shapeThumbnail:"Shape: Thumbnail",shapeNone:"Shape: None",dragImageHere:"Drag image or text here",dropImage:"Drop image or Text",selectFromFiles:"Select from files",maximumFileSize:"Maximum file size",maximumFileSizeError:"Maximum file size exceeded.",url:"Image URL",remove:"Remove Image",original:"Original"},video:{video:"Video",videoLink:"Video Link",insert:"Insert Video",url:"Video URL",providers:"(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)"},link:{link:"Link",insert:"Insert Link",unlink:"Unlink",edit:"Edit",textToDisplay:"Text to display",url:"To what URL should this link go?",openInNewWindow:"Open in new window",useProtocol:"Use default protocol"},table:{table:"Table",addRowAbove:"Add row above",addRowBelow:"Add row below",addColLeft:"Add column left",addColRight:"Add column right",delRow:"Delete row",delCol:"Delete column",delTable:"Delete table"},hr:{insert:"Insert Horizontal Rule"},style:{style:"Style",p:"Normal",blockquote:"Quote",pre:"Code",h1:"Header 1",h2:"Header 2",h3:"Header 3",h4:"Header 4",h5:"Header 5",h6:"Header 6"},lists:{unordered:"Unordered list",ordered:"Ordered list"},options:{help:"Help",fullscreen:"Full Screen",codeview:"Code View"},paragraph:{paragraph:"Paragraph",outdent:"Outdent",indent:"Indent",left:"Align left",center:"Align center",right:"Align right",justify:"Justify full"},color:{recent:"Recent Color",more:"More Color",background:"Background Color",foreground:"Text Color",transparent:"Transparent",setTransparent:"Set transparent",reset:"Reset",resetToDefault:"Reset to default",cpSelect:"Select"},shortcut:{shortcuts:"Keyboard shortcuts",close:"Close",textFormatting:"Text formatting",action:"Action",paragraphFormatting:"Paragraph formatting",documentStyle:"Document Style",extraKeys:"Extra keys"},help:{insertParagraph:"Insert Paragraph",undo:"Undoes the last command",redo:"Redoes the last command",tab:"Tab",untab:"Untab",bold:"Set a bold style",italic:"Set a italic style",underline:"Set a underline style",strikethrough:"Set a strikethrough style",removeFormat:"Clean a style",justifyLeft:"Set left align",justifyCenter:"Set center align",justifyRight:"Set right align",justifyFull:"Set full align",insertUnorderedList:"Toggle unordered list",insertOrderedList:"Toggle ordered list",outdent:"Outdent on current paragraph",indent:"Indent on current paragraph",formatPara:"Change current block's format as a paragraph(P tag)",formatH1:"Change current block's format as H1",formatH2:"Change current block's format as H2",formatH3:"Change current block's format as H3",formatH4:"Change current block's format as H4",formatH5:"Change current block's format as H5",formatH6:"Change current block's format as H6",insertHorizontalRule:"Insert horizontal rule","linkDialog.show":"Show Link Dialog"},history:{undo:"Undo",redo:"Redo"},specialChar:{specialChar:"SPECIAL CHARACTERS",select:"Select Special characters"},output:{noSelection:"No Selection Made!"}}});const s="function"==typeof define&&o(2),r=["sans-serif","serif","monospace","cursive","fantasy"];function a(t){return-1===n.a.inArray(t.toLowerCase(),r)?`'${t}'`:t}const l=navigator.userAgent,c=/MSIE|Trident/i.test(l);let d;if(c){let t=/MSIE (\d+[.]\d+)/.exec(l);t&&(d=parseFloat(t[1])),t=/Trident\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(l),t&&(d=parseFloat(t[1]))}const h=/Edge\/\d+/.test(l);let u=!!window.CodeMirror;const p="ontouchstart"in window||navigator.MaxTouchPoints>0||navigator.msMaxTouchPoints>0,m=c?"DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted":"input";var f={isMac:navigator.appVersion.indexOf("Mac")>-1,isMSIE:c,isEdge:h,isFF:!h&&/firefox/i.test(l),isPhantom:/PhantomJS/i.test(l),isWebkit:!h&&/webkit/i.test(l),isChrome:!h&&/chrome/i.test(l),isSafari:!h&&/safari/i.test(l)&&!/chrome/i.test(l),browserVersion:d,jqueryVersion:parseFloat(n.a.fn.jquery),isSupportAmd:s,isSupportTouch:p,hasCodeMirror:u,isFontInstalled:function(t){const e="Comic Sans MS"===t?"Courier New":"Comic Sans MS";var o=document.createElement("canvas").getContext("2d");o.font="200px '"+e+"'";const i=o.measureText("mmmmmmmmmmwwwww").width;return o.font="200px "+a(t)+', "'+e+'"',i!==o.measureText("mmmmmmmmmmwwwww").width},isW3CRangeSupport:!!document.createRange,inputEventName:m,genericFontFamilies:r,validFontName:a};let g=0;var b={eq:function(t){return function(e){return t===e}},eq2:function(t,e){return t===e},peq2:function(t){return function(e,o){return e[t]===o[t]}},ok:function(){return!0},fail:function(){return!1},self:function(t){return t},not:function(t){return function(){return!t.apply(t,arguments)}},and:function(t,e){return function(o){return t(o)&&e(o)}},invoke:function(t,e){return function(){return t[e].apply(t,arguments)}},resetUniqueId:function(){g=0},uniqueId:function(t){const e=++g+"";return t?t+e:e},rect2bnd:function(t){const e=n()(document);return{top:t.top+e.scrollTop(),left:t.left+e.scrollLeft(),width:t.right-t.left,height:t.bottom-t.top}},invertObject:function(t){const e={};for(const o in t)t.hasOwnProperty(o)&&(e[t[o]]=o);return e},namespaceToCamel:function(t,e){return(e=e||"")+t.split(".").map((function(t){return t.substring(0,1).toUpperCase()+t.substring(1)})).join("")},debounce:function(t,e,o){let i;return function(){const n=this,s=arguments,r=()=>{i=null,o||t.apply(n,s)},a=o&&!i;clearTimeout(i),i=setTimeout(r,e),a&&t.apply(n,s)}},isValidUrl:function(t){return/[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/gi.test(t)}};function v(t){return t[0]}function k(t){return t[t.length-1]}function C(t){return t.slice(1)}function w(t,e){if(t&&t.length&&e){if(t.indexOf)return-1!==t.indexOf(e);if(t.contains)return t.contains(e)}return!1}var y={head:v,last:k,initial:function(t){return t.slice(0,t.length-1)},tail:C,prev:function(t,e){if(t&&t.length&&e){const o=t.indexOf(e);return-1===o?null:t[o-1]}return null},next:function(t,e){if(t&&t.length&&e){const o=t.indexOf(e);return-1===o?null:t[o+1]}return null},find:function(t,e){for(let o=0,i=t.length;o<i;o++){const i=t[o];if(e(i))return i}},contains:w,all:function(t,e){for(let o=0,i=t.length;o<i;o++)if(!e(t[o]))return!1;return!0},sum:function(t,e){return e=e||b.self,t.reduce((function(t,o){return t+e(o)}),0)},from:function(t){const e=[],o=t.length;let i=-1;for(;++i<o;)e[i]=t[i];return e},isEmpty:function(t){return!t||!t.length},clusterBy:function(t,e){return t.length?C(t).reduce((function(t,o){const i=k(t);return e(k(i),o)?i[i.length]=o:t[t.length]=[o],t}),[[v(t)]]):[]},compact:function(t){const e=[];for(let o=0,i=t.length;o<i;o++)t[o]&&e.push(t[o]);return e},unique:function(t){const e=[];for(let o=0,i=t.length;o<i;o++)w(e,t[o])||e.push(t[o]);return e}};const x=String.fromCharCode(160);function S(t){return t&&n()(t).hasClass("note-editable")}function $(t){return t=t.toUpperCase(),function(e){return e&&e.nodeName.toUpperCase()===t}}function I(t){return t&&3===t.nodeType}function N(t){return t&&/^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^AUDIO|^VIDEO|^EMBED/.test(t.nodeName.toUpperCase())}function T(t){return!S(t)&&(t&&/^DIV|^P|^LI|^H[1-7]/.test(t.nodeName.toUpperCase()))}const E=$("PRE"),R=$("LI");const L=$("TABLE"),A=$("DATA");function P(t){return!(z(t)||F(t)||D(t)||T(t)||L(t)||B(t)||A(t))}function F(t){return t&&/^UL|^OL/.test(t.nodeName.toUpperCase())}const D=$("HR");function H(t){return t&&/^TD|^TH/.test(t.nodeName.toUpperCase())}const B=$("BLOCKQUOTE");function z(t){return H(t)||B(t)||S(t)}const M=$("A");const O=$("BODY");const U=f.isMSIE&&f.browserVersion<11?"&nbsp;":"<br>";function j(t){return I(t)?t.nodeValue.length:t?t.childNodes.length:0}function W(t){const e=j(t);return 0===e||(!I(t)&&1===e&&t.innerHTML===U||!(!y.all(t.childNodes,I)||""!==t.innerHTML))}function K(t){N(t)||j(t)||(t.innerHTML=U)}function q(t,e){for(;t;){if(e(t))return t;if(S(t))break;t=t.parentNode}return null}function V(t,e){e=e||b.fail;const o=[];return q(t,(function(t){return S(t)||o.push(t),e(t)})),o}function _(t,e){e=e||b.fail;const o=[];for(;t&&!e(t);)o.push(t),t=t.nextSibling;return o}function G(t,e){const o=e.nextSibling;let i=e.parentNode;return o?i.insertBefore(t,o):i.appendChild(t),t}function Z(t,e){return n.a.each(e,(function(e,o){t.appendChild(o)})),t}function Y(t){return 0===t.offset}function Q(t){return t.offset===j(t.node)}function X(t){return Y(t)||Q(t)}function J(t,e){for(;t&&t!==e;){if(0!==et(t))return!1;t=t.parentNode}return!0}function tt(t,e){if(!e)return!1;for(;t&&t!==e;){if(et(t)!==j(t.parentNode)-1)return!1;t=t.parentNode}return!0}function et(t){let e=0;for(;t=t.previousSibling;)e+=1;return e}function ot(t){return!!(t&&t.childNodes&&t.childNodes.length)}function it(t,e){let o,i;if(0===t.offset){if(S(t.node))return null;o=t.node.parentNode,i=et(t.node)}else ot(t.node)?(o=t.node.childNodes[t.offset-1],i=j(o)):(o=t.node,i=e?0:t.offset-1);return{node:o,offset:i}}function nt(t,e){let o,i;if(W(t.node))return null;if(j(t.node)===t.offset){if(S(t.node))return null;o=t.node.parentNode,i=et(t.node)+1}else if(ot(t.node)){if(o=t.node.childNodes[t.offset],i=0,W(o))return null}else if(o=t.node,i=e?j(t.node):t.offset+1,W(o))return null;return{node:o,offset:i}}function st(t,e){return t.node===e.node&&t.offset===e.offset}function rt(t,e){let o=e&&e.isSkipPaddingBlankHTML;const i=e&&e.isNotSplitEdgePoint,n=e&&e.isDiscardEmptySplits;if(n&&(o=!0),X(t)&&(I(t.node)||i)){if(Y(t))return t.node;if(Q(t))return t.node.nextSibling}if(I(t.node))return t.node.splitText(t.offset);{const e=t.node.childNodes[t.offset],i=G(t.node.cloneNode(!1),t.node);return Z(i,_(e)),o||(K(t.node),K(i)),n&&(W(t.node)&&ct(t.node),W(i))?(ct(i),t.node.nextSibling):i}}function at(t,e,o){const i=V(e.node,b.eq(t));return i.length?1===i.length?rt(e,o):i.reduce((function(t,i){return t===e.node&&(t=rt(e,o)),rt({node:i,offset:t?et(t):j(i)},o)})):null}function lt(t){return document.createElement(t)}function ct(t,e){if(!t||!t.parentNode)return;if(t.removeNode)return t.removeNode(e);const o=t.parentNode;if(!e){const e=[];for(let o=0,i=t.childNodes.length;o<i;o++)e.push(t.childNodes[o]);for(let i=0,n=e.length;i<n;i++)o.insertBefore(e[i],t)}o.removeChild(t)}const dt=$("TEXTAREA");function ht(t,e){const o=dt(t[0])?t.val():t.html();return e?o.replace(/[\n\r]/g,""):o}var ut={NBSP_CHAR:x,ZERO_WIDTH_NBSP_CHAR:"\ufeff",blank:U,emptyPara:`<p>${U}</p>`,makePredByNodeName:$,isEditable:S,isControlSizing:function(t){return t&&n()(t).hasClass("note-control-sizing")},isText:I,isElement:function(t){return t&&1===t.nodeType},isVoid:N,isPara:T,isPurePara:function(t){return T(t)&&!R(t)},isHeading:function(t){return t&&/^H[1-7]/.test(t.nodeName.toUpperCase())},isInline:P,isBlock:b.not(P),isBodyInline:function(t){return P(t)&&!q(t,T)},isBody:O,isParaInline:function(t){return P(t)&&!!q(t,T)},isPre:E,isList:F,isTable:L,isData:A,isCell:H,isBlockquote:B,isBodyContainer:z,isAnchor:M,isDiv:$("DIV"),isLi:R,isBR:$("BR"),isSpan:$("SPAN"),isB:$("B"),isU:$("U"),isS:$("S"),isI:$("I"),isImg:$("IMG"),isTextarea:dt,deepestChildIsEmpty:function(t){do{if(null===t.firstElementChild||""===t.firstElementChild.innerHTML)break}while(t=t.firstElementChild);return W(t)},isEmpty:W,isEmptyAnchor:b.and(M,W),isClosestSibling:function(t,e){return t.nextSibling===e||t.previousSibling===e},withClosestSiblings:function(t,e){e=e||b.ok;const o=[];return t.previousSibling&&e(t.previousSibling)&&o.push(t.previousSibling),o.push(t),t.nextSibling&&e(t.nextSibling)&&o.push(t.nextSibling),o},nodeLength:j,isLeftEdgePoint:Y,isRightEdgePoint:Q,isEdgePoint:X,isLeftEdgeOf:J,isRightEdgeOf:tt,isLeftEdgePointOf:function(t,e){return Y(t)&&J(t.node,e)},isRightEdgePointOf:function(t,e){return Q(t)&&tt(t.node,e)},prevPoint:it,nextPoint:nt,isSamePoint:st,isVisiblePoint:function(t){if(I(t.node)||!ot(t.node)||W(t.node))return!0;const e=t.node.childNodes[t.offset-1],o=t.node.childNodes[t.offset];return!(e&&!N(e)||o&&!N(o))},prevPointUntil:function(t,e){for(;t;){if(e(t))return t;t=it(t)}return null},nextPointUntil:function(t,e){for(;t;){if(e(t))return t;t=nt(t)}return null},isCharPoint:function(t){if(!I(t.node))return!1;const e=t.node.nodeValue.charAt(t.offset-1);return e&&" "!==e&&e!==x},isSpacePoint:function(t){if(!I(t.node))return!1;const e=t.node.nodeValue.charAt(t.offset-1);return" "===e||e===x},walkPoint:function(t,e,o,i){let n=t;for(;n&&(o(n),!st(n,e));){n=nt(n,i&&t.node!==n.node&&e.node!==n.node)}},ancestor:q,singleChildAncestor:function(t,e){for(t=t.parentNode;t&&1===j(t);){if(e(t))return t;if(S(t))break;t=t.parentNode}return null},listAncestor:V,lastAncestor:function(t,e){const o=V(t);return y.last(o.filter(e))},listNext:_,listPrev:function(t,e){e=e||b.fail;const o=[];for(;t&&!e(t);)o.push(t),t=t.previousSibling;return o},listDescendant:function(t,e){const o=[];return e=e||b.ok,function i(n){t!==n&&e(n)&&o.push(n);for(let t=0,e=n.childNodes.length;t<e;t++)i(n.childNodes[t])}(t),o},commonAncestor:function(t,e){const o=V(t);for(let t=e;t;t=t.parentNode)if(o.indexOf(t)>-1)return t;return null},wrap:function(t,e){const o=t.parentNode,i=n()("<"+e+">")[0];return o.insertBefore(i,t),i.appendChild(t),i},insertAfter:G,appendChildNodes:Z,position:et,hasChildren:ot,makeOffsetPath:function(t,e){return V(e,b.eq(t)).map(et).reverse()},fromOffsetPath:function(t,e){let o=t;for(let t=0,i=e.length;t<i;t++)o=o.childNodes.length<=e[t]?o.childNodes[o.childNodes.length-1]:o.childNodes[e[t]];return o},splitTree:at,splitPoint:function(t,e){const o=e?T:z,i=V(t.node,o),n=y.last(i)||t.node;let s,r;o(n)?(s=i[i.length-2],r=n):(s=n,r=s.parentNode);let a=s&&at(s,t,{isSkipPaddingBlankHTML:e,isNotSplitEdgePoint:e});return a||r!==t.node||(a=t.node.childNodes[t.offset]),{rightNode:a,container:r}},create:lt,createText:function(t){return document.createTextNode(t)},remove:ct,removeWhile:function(t,e){for(;t&&!S(t)&&e(t);){const e=t.parentNode;ct(t),t=e}},replace:function(t,e){if(t.nodeName.toUpperCase()===e.toUpperCase())return t;const o=lt(e);return t.style.cssText&&(o.style.cssText=t.style.cssText),Z(o,y.from(t.childNodes)),G(o,t),ct(t),o},html:function(t,e){let o=ht(t);if(e){const t=/<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g;o=o.replace(t,(function(t,e,o){o=o.toUpperCase();const i=/^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(o)&&!!e,n=/^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(o);return t+(i||n?"\n":"")})),o=o.trim()}return o},value:ht,posFromPlaceholder:function(t){const e=n()(t),o=e.offset(),i=e.outerHeight(!0);return{left:o.left,top:o.top+i}},attachEvents:function(t,e){Object.keys(e).forEach((function(o){t.on(o,e[o])}))},detachEvents:function(t,e){Object.keys(e).forEach((function(o){t.off(o,e[o])}))},isCustomStyleTag:function(t){return t&&!I(t)&&y.contains(t.classList,"note-styletag")}};class pt{constructor(t,e){this.$note=t,this.memos={},this.modules={},this.layoutInfo={},this.options=n.a.extend(!0,{},e),n.a.summernote.ui=n.a.summernote.ui_template(this.options),this.ui=n.a.summernote.ui,this.initialize()}initialize(){return this.layoutInfo=this.ui.createLayout(this.$note),this._initialize(),this.$note.hide(),this}destroy(){this._destroy(),this.$note.removeData("summernote"),this.ui.removeLayout(this.$note,this.layoutInfo)}reset(){const t=this.isDisabled();this.code(ut.emptyPara),this._destroy(),this._initialize(),t&&this.disable()}_initialize(){this.options.id=b.uniqueId(n.a.now()),this.options.container=this.options.container||this.layoutInfo.editor;const t=n.a.extend({},this.options.buttons);Object.keys(t).forEach(e=>{this.memo("button."+e,t[e])});const e=n.a.extend({},this.options.modules,n.a.summernote.plugins||{});Object.keys(e).forEach(t=>{this.module(t,e[t],!0)}),Object.keys(this.modules).forEach(t=>{this.initializeModule(t)})}_destroy(){Object.keys(this.modules).reverse().forEach(t=>{this.removeModule(t)}),Object.keys(this.memos).forEach(t=>{this.removeMemo(t)}),this.triggerEvent("destroy",this)}code(t){const e=this.invoke("codeview.isActivated");if(void 0===t)return this.invoke("codeview.sync"),e?this.layoutInfo.codable.val():this.layoutInfo.editable.html();e?this.layoutInfo.codable.val(t):this.layoutInfo.editable.html(t),this.$note.val(t),this.triggerEvent("change",t,this.layoutInfo.editable)}isDisabled(){return"false"===this.layoutInfo.editable.attr("contenteditable")}enable(){this.layoutInfo.editable.attr("contenteditable",!0),this.invoke("toolbar.activate",!0),this.triggerEvent("disable",!1),this.options.editing=!0}disable(){this.invoke("codeview.isActivated")&&this.invoke("codeview.deactivate"),this.layoutInfo.editable.attr("contenteditable",!1),this.options.editing=!1,this.invoke("toolbar.deactivate",!0),this.triggerEvent("disable",!0)}triggerEvent(){const t=y.head(arguments),e=y.tail(y.from(arguments)),o=this.options.callbacks[b.namespaceToCamel(t,"on")];o&&o.apply(this.$note[0],e),this.$note.trigger("summernote."+t,e)}initializeModule(t){const e=this.modules[t];e.shouldInitialize=e.shouldInitialize||b.ok,e.shouldInitialize()&&(e.initialize&&e.initialize(),e.events&&ut.attachEvents(this.$note,e.events))}module(t,e,o){if(1===arguments.length)return this.modules[t];this.modules[t]=new e(this),o||this.initializeModule(t)}removeModule(t){const e=this.modules[t];e.shouldInitialize()&&(e.events&&ut.detachEvents(this.$note,e.events),e.destroy&&e.destroy()),delete this.modules[t]}memo(t,e){if(1===arguments.length)return this.memos[t];this.memos[t]=e}removeMemo(t){this.memos[t]&&this.memos[t].destroy&&this.memos[t].destroy(),delete this.memos[t]}createInvokeHandlerAndUpdateState(t,e){return o=>{this.createInvokeHandler(t,e)(o),this.invoke("buttons.updateCurrentStyle")}}createInvokeHandler(t,e){return o=>{o.preventDefault();const i=n()(o.target);this.invoke(t,e||i.closest("[data-value]").data("value"),i)}}invoke(){const t=y.head(arguments),e=y.tail(y.from(arguments)),o=t.split("."),i=o.length>1,n=i&&y.head(o),s=i?y.last(o):y.head(o),r=this.modules[n||"editor"];return!n&&this[s]?this[s].apply(this,e):r&&r[s]&&r.shouldInitialize()?r[s].apply(r,e):void 0}}function mt(t,e){let o,i=t.parentElement();const n=document.body.createTextRange();let s;const r=y.from(i.childNodes);for(o=0;o<r.length;o++)if(!ut.isText(r[o])){if(n.moveToElementText(r[o]),n.compareEndPoints("StartToStart",t)>=0)break;s=r[o]}if(0!==o&&ut.isText(r[o-1])){const n=document.body.createTextRange();let r=null;n.moveToElementText(s||i),n.collapse(!s),r=s?s.nextSibling:i.firstChild;const a=t.duplicate();a.setEndPoint("StartToStart",n);let l=a.text.replace(/[\r\n]/g,"").length;for(;l>r.nodeValue.length&&r.nextSibling;)l-=r.nodeValue.length,r=r.nextSibling;r.nodeValue;e&&r.nextSibling&&ut.isText(r.nextSibling)&&l===r.nodeValue.length&&(l-=r.nodeValue.length,r=r.nextSibling),i=r,o=l}return{cont:i,offset:o}}function ft(t){const e=function(t,o){let i,n;if(ut.isText(t)){const e=ut.listPrev(t,b.not(ut.isText)),s=y.last(e).previousSibling;i=s||t.parentNode,o+=y.sum(y.tail(e),ut.nodeLength),n=!s}else{if(i=t.childNodes[o]||t,ut.isText(i))return e(i,0);o=0,n=!1}return{node:i,collapseToStart:n,offset:o}},o=document.body.createTextRange(),i=e(t.node,t.offset);return o.moveToElementText(i.node),o.collapse(i.collapseToStart),o.moveStart("character",i.offset),o}n.a.fn.extend({summernote:function(){const t=n.a.type(y.head(arguments)),e="string"===t,o="object"===t,i=n.a.extend({},n.a.summernote.options,o?y.head(arguments):{});i.langInfo=n.a.extend(!0,{},n.a.summernote.lang["en-US"],n.a.summernote.lang[i.lang]),i.icons=n.a.extend(!0,{},n.a.summernote.options.icons,i.icons),i.tooltip="auto"===i.tooltip?!f.isSupportTouch:i.tooltip,this.each((t,e)=>{const o=n()(e);if(!o.data("summernote")){const t=new pt(o,i);o.data("summernote",t),o.data("summernote").triggerEvent("init",t.layoutInfo)}});const s=this.first();if(s.length){const t=s.data("summernote");if(e)return t.invoke.apply(t,y.from(arguments));i.focus&&t.invoke("editor.focus")}return this}});class gt{constructor(t,e,o,i){this.sc=t,this.so=e,this.ec=o,this.eo=i,this.isOnEditable=this.makeIsOn(ut.isEditable),this.isOnList=this.makeIsOn(ut.isList),this.isOnAnchor=this.makeIsOn(ut.isAnchor),this.isOnCell=this.makeIsOn(ut.isCell),this.isOnData=this.makeIsOn(ut.isData)}nativeRange(){if(f.isW3CRangeSupport){const t=document.createRange();return t.setStart(this.sc,this.sc.data&&this.so>this.sc.data.length?0:this.so),t.setEnd(this.ec,this.sc.data?Math.min(this.eo,this.sc.data.length):this.eo),t}{const t=ft({node:this.sc,offset:this.so});return t.setEndPoint("EndToEnd",ft({node:this.ec,offset:this.eo})),t}}getPoints(){return{sc:this.sc,so:this.so,ec:this.ec,eo:this.eo}}getStartPoint(){return{node:this.sc,offset:this.so}}getEndPoint(){return{node:this.ec,offset:this.eo}}select(){const t=this.nativeRange();if(f.isW3CRangeSupport){const e=document.getSelection();e.rangeCount>0&&e.removeAllRanges(),e.addRange(t)}else t.select();return this}scrollIntoView(t){const e=n()(t).height();return t.scrollTop+e<this.sc.offsetTop&&(t.scrollTop+=Math.abs(t.scrollTop+e-this.sc.offsetTop)),this}normalize(){const t=function(t,e){if(!t)return t;if(ut.isVisiblePoint(t)&&(!ut.isEdgePoint(t)||ut.isRightEdgePoint(t)&&!e||ut.isLeftEdgePoint(t)&&e||ut.isRightEdgePoint(t)&&e&&ut.isVoid(t.node.nextSibling)||ut.isLeftEdgePoint(t)&&!e&&ut.isVoid(t.node.previousSibling)||ut.isBlock(t.node)&&ut.isEmpty(t.node)))return t;const o=ut.ancestor(t.node,ut.isBlock);let i=!1;if(!i){const n=ut.prevPoint(t)||{node:null};i=(ut.isLeftEdgePointOf(t,o)||ut.isVoid(n.node))&&!e}let n=!1;if(!n){const i=ut.nextPoint(t)||{node:null};n=(ut.isRightEdgePointOf(t,o)||ut.isVoid(i.node))&&e}if(i||n){if(ut.isVisiblePoint(t))return t;e=!e}return(e?ut.nextPointUntil(ut.nextPoint(t),ut.isVisiblePoint):ut.prevPointUntil(ut.prevPoint(t),ut.isVisiblePoint))||t},e=t(this.getEndPoint(),!1),o=this.isCollapsed()?e:t(this.getStartPoint(),!0);return new gt(o.node,o.offset,e.node,e.offset)}nodes(t,e){t=t||b.ok;const o=e&&e.includeAncestor,i=e&&e.fullyContains,n=this.getStartPoint(),s=this.getEndPoint(),r=[],a=[];return ut.walkPoint(n,s,(function(e){if(ut.isEditable(e.node))return;let n;i?(ut.isLeftEdgePoint(e)&&a.push(e.node),ut.isRightEdgePoint(e)&&y.contains(a,e.node)&&(n=e.node)):n=o?ut.ancestor(e.node,t):e.node,n&&t(n)&&r.push(n)}),!0),y.unique(r)}commonAncestor(){return ut.commonAncestor(this.sc,this.ec)}expand(t){const e=ut.ancestor(this.sc,t),o=ut.ancestor(this.ec,t);if(!e&&!o)return new gt(this.sc,this.so,this.ec,this.eo);const i=this.getPoints();return e&&(i.sc=e,i.so=0),o&&(i.ec=o,i.eo=ut.nodeLength(o)),new gt(i.sc,i.so,i.ec,i.eo)}collapse(t){return t?new gt(this.sc,this.so,this.sc,this.so):new gt(this.ec,this.eo,this.ec,this.eo)}splitText(){const t=this.sc===this.ec,e=this.getPoints();return ut.isText(this.ec)&&!ut.isEdgePoint(this.getEndPoint())&&this.ec.splitText(this.eo),ut.isText(this.sc)&&!ut.isEdgePoint(this.getStartPoint())&&(e.sc=this.sc.splitText(this.so),e.so=0,t&&(e.ec=e.sc,e.eo=this.eo-this.so)),new gt(e.sc,e.so,e.ec,e.eo)}deleteContents(){if(this.isCollapsed())return this;const t=this.splitText(),e=t.nodes(null,{fullyContains:!0}),o=ut.prevPointUntil(t.getStartPoint(),(function(t){return!y.contains(e,t.node)})),i=[];return n.a.each(e,(function(t,e){const n=e.parentNode;o.node!==n&&1===ut.nodeLength(n)&&i.push(n),ut.remove(e,!1)})),n.a.each(i,(function(t,e){ut.remove(e,!1)})),new gt(o.node,o.offset,o.node,o.offset).normalize()}makeIsOn(t){return function(){const e=ut.ancestor(this.sc,t);return!!e&&e===ut.ancestor(this.ec,t)}}isLeftEdgeOf(t){if(!ut.isLeftEdgePoint(this.getStartPoint()))return!1;const e=ut.ancestor(this.sc,t);return e&&ut.isLeftEdgeOf(this.sc,e)}isCollapsed(){return this.sc===this.ec&&this.so===this.eo}wrapBodyInlineWithPara(){if(ut.isBodyContainer(this.sc)&&ut.isEmpty(this.sc))return this.sc.innerHTML=ut.emptyPara,new gt(this.sc.firstChild,0,this.sc.firstChild,0);const t=this.normalize();if(ut.isParaInline(this.sc)||ut.isPara(this.sc))return t;let e;if(ut.isInline(t.sc)){const o=ut.listAncestor(t.sc,b.not(ut.isInline));e=y.last(o),ut.isInline(e)||(e=o[o.length-2]||t.sc.childNodes[t.so])}else e=t.sc.childNodes[t.so>0?t.so-1:0];if(e){let t=ut.listPrev(e,ut.isParaInline).reverse();if(t=t.concat(ut.listNext(e.nextSibling,ut.isParaInline)),t.length){const e=ut.wrap(y.head(t),"p");ut.appendChildNodes(e,y.tail(t))}}return this.normalize()}insertNode(t){let e=this;(ut.isText(t)||ut.isInline(t))&&(e=this.wrapBodyInlineWithPara().deleteContents());const o=ut.splitPoint(e.getStartPoint(),ut.isInline(t));return o.rightNode?o.rightNode.parentNode.insertBefore(t,o.rightNode):o.container.appendChild(t),t}pasteHTML(t){t=n.a.trim(t);const e=n()("<div></div>").html(t)[0];let o=y.from(e.childNodes);const i=this;return i.so>=0&&(o=o.reverse()),o=o.map((function(t){return i.insertNode(t)})),i.so>0&&(o=o.reverse()),o}toString(){const t=this.nativeRange();return f.isW3CRangeSupport?t.toString():t.text}getWordRange(t){let e=this.getEndPoint();if(!ut.isCharPoint(e))return this;const o=ut.prevPointUntil(e,(function(t){return!ut.isCharPoint(t)}));return t&&(e=ut.nextPointUntil(e,(function(t){return!ut.isCharPoint(t)}))),new gt(o.node,o.offset,e.node,e.offset)}getWordsRange(t){var e=this.getEndPoint(),o=function(t){return!ut.isCharPoint(t)&&!ut.isSpacePoint(t)};if(o(e))return this;var i=ut.prevPointUntil(e,o);return t&&(e=ut.nextPointUntil(e,o)),new gt(i.node,i.offset,e.node,e.offset)}getWordsMatchRange(t){var e=this.getEndPoint(),o=ut.prevPointUntil(e,(function(o){if(!ut.isCharPoint(o)&&!ut.isSpacePoint(o))return!0;var i=new gt(o.node,o.offset,e.node,e.offset),n=t.exec(i.toString());return n&&0===n.index})),i=new gt(o.node,o.offset,e.node,e.offset),n=i.toString(),s=t.exec(n);return s&&s[0].length===n.length?i:null}bookmark(t){return{s:{path:ut.makeOffsetPath(t,this.sc),offset:this.so},e:{path:ut.makeOffsetPath(t,this.ec),offset:this.eo}}}paraBookmark(t){return{s:{path:y.tail(ut.makeOffsetPath(y.head(t),this.sc)),offset:this.so},e:{path:y.tail(ut.makeOffsetPath(y.last(t),this.ec)),offset:this.eo}}}getClientRects(){return this.nativeRange().getClientRects()}}var bt={create:function(t,e,o,i){if(4===arguments.length)return new gt(t,e,o,i);if(2===arguments.length)return new gt(t,e,o=t,i=e);{let t=this.createFromSelection();if(!t&&1===arguments.length){let t=arguments[0];return ut.isEditable(t)&&(t=t.lastChild),this.createFromBodyElement(t,ut.emptyPara===arguments[0].innerHTML)}return t}},createFromBodyElement:function(t,e=!1){return this.createFromNode(t).collapse(e)},createFromSelection:function(){let t,e,o,i;if(f.isW3CRangeSupport){const n=document.getSelection();if(!n||0===n.rangeCount)return null;if(ut.isBody(n.anchorNode))return null;const s=n.getRangeAt(0);t=s.startContainer,e=s.startOffset,o=s.endContainer,i=s.endOffset}else{const n=document.selection.createRange(),s=n.duplicate();s.collapse(!1);const r=n;r.collapse(!0);let a=mt(r,!0),l=mt(s,!1);ut.isText(a.node)&&ut.isLeftEdgePoint(a)&&ut.isTextNode(l.node)&&ut.isRightEdgePoint(l)&&l.node.nextSibling===a.node&&(a=l),t=a.cont,e=a.offset,o=l.cont,i=l.offset}return new gt(t,e,o,i)},createFromNode:function(t){let e=t,o=0,i=t,n=ut.nodeLength(i);return ut.isVoid(e)&&(o=ut.listPrev(e).length-1,e=e.parentNode),ut.isBR(i)?(n=ut.listPrev(i).length-1,i=i.parentNode):ut.isVoid(i)&&(n=ut.listPrev(i).length,i=i.parentNode),this.create(e,o,i,n)},createFromNodeBefore:function(t){return this.createFromNode(t).collapse(!0)},createFromNodeAfter:function(t){return this.createFromNode(t).collapse()},createFromBookmark:function(t,e){const o=ut.fromOffsetPath(t,e.s.path),i=e.s.offset,n=ut.fromOffsetPath(t,e.e.path),s=e.e.offset;return new gt(o,i,n,s)},createFromParaBookmark:function(t,e){const o=t.s.offset,i=t.e.offset,n=ut.fromOffsetPath(y.head(e),t.s.path),s=ut.fromOffsetPath(y.last(e),t.e.path);return new gt(n,o,s,i)}};const vt={BACKSPACE:8,TAB:9,ENTER:13,SPACE:32,DELETE:46,LEFT:37,UP:38,RIGHT:39,DOWN:40,NUM0:48,NUM1:49,NUM2:50,NUM3:51,NUM4:52,NUM5:53,NUM6:54,NUM7:55,NUM8:56,B:66,E:69,I:73,J:74,K:75,L:76,R:82,S:83,U:85,V:86,Y:89,Z:90,SLASH:191,LEFTBRACKET:219,BACKSLASH:220,RIGHTBRACKET:221,HOME:36,END:35,PAGEUP:33,PAGEDOWN:34};var kt={isEdit:t=>y.contains([vt.BACKSPACE,vt.TAB,vt.ENTER,vt.SPACE,vt.DELETE],t),isMove:t=>y.contains([vt.LEFT,vt.UP,vt.RIGHT,vt.DOWN],t),isNavigation:t=>y.contains([vt.HOME,vt.END,vt.PAGEUP,vt.PAGEDOWN],t),nameFromCode:b.invertObject(vt),code:vt};class Ct{constructor(t){this.stack=[],this.stackOffset=-1,this.$editable=t,this.editable=t[0]}makeSnapshot(){const t=bt.create(this.editable);return{contents:this.$editable.html(),bookmark:t&&t.isOnEditable()?t.bookmark(this.editable):{s:{path:[],offset:0},e:{path:[],offset:0}}}}applySnapshot(t){null!==t.contents&&this.$editable.html(t.contents),null!==t.bookmark&&bt.createFromBookmark(this.editable,t.bookmark).select()}rewind(){this.$editable.html()!==this.stack[this.stackOffset].contents&&this.recordUndo(),this.stackOffset=0,this.applySnapshot(this.stack[this.stackOffset])}commit(){this.stack=[],this.stackOffset=-1,this.recordUndo()}reset(){this.stack=[],this.stackOffset=-1,this.$editable.html(""),this.recordUndo()}undo(){this.$editable.html()!==this.stack[this.stackOffset].contents&&this.recordUndo(),this.stackOffset>0&&(this.stackOffset--,this.applySnapshot(this.stack[this.stackOffset]))}redo(){this.stack.length-1>this.stackOffset&&(this.stackOffset++,this.applySnapshot(this.stack[this.stackOffset]))}recordUndo(){this.stackOffset++,this.stack.length>this.stackOffset&&(this.stack=this.stack.slice(0,this.stackOffset)),this.stack.push(this.makeSnapshot())}}class wt{jQueryCSS(t,e){if(f.jqueryVersion<1.9){const o={};return n.a.each(e,(e,i)=>{o[i]=t.css(i)}),o}return t.css(e)}fromNode(t){const e=this.jQueryCSS(t,["font-family","font-size","text-align","list-style-type","line-height"])||{},o=t[0].style.fontSize||e["font-size"];return e["font-size"]=parseInt(o,10),e["font-size-unit"]=o.match(/[a-z%]+$/),e}stylePara(t,e){n.a.each(t.nodes(ut.isPara,{includeAncestor:!0}),(t,o)=>{n()(o).css(e)})}styleNodes(t,e){t=t.splitText();const o=e&&e.nodeName||"SPAN",i=!(!e||!e.expandClosestSibling),s=!(!e||!e.onlyPartialContains);if(t.isCollapsed())return[t.insertNode(ut.create(o))];let r=ut.makePredByNodeName(o);const a=t.nodes(ut.isText,{fullyContains:!0}).map(t=>ut.singleChildAncestor(t,r)||ut.wrap(t,o));if(i){if(s){const e=t.nodes();r=b.and(r,t=>y.contains(e,t))}return a.map(t=>{const e=ut.withClosestSiblings(t,r),o=y.head(e),i=y.tail(e);return n.a.each(i,(t,e)=>{ut.appendChildNodes(o,e.childNodes),ut.remove(e)}),y.head(e)})}return a}current(t){const e=n()(ut.isElement(t.sc)?t.sc:t.sc.parentNode);let o=this.fromNode(e);try{o=n.a.extend(o,{"font-bold":document.queryCommandState("bold")?"bold":"normal","font-italic":document.queryCommandState("italic")?"italic":"normal","font-underline":document.queryCommandState("underline")?"underline":"normal","font-subscript":document.queryCommandState("subscript")?"subscript":"normal","font-superscript":document.queryCommandState("superscript")?"superscript":"normal","font-strikethrough":document.queryCommandState("strikethrough")?"strikethrough":"normal","font-family":document.queryCommandValue("fontname")||o["font-family"]})}catch(t){}if(t.isOnList()){const t=["circle","disc","disc-leading-zero","square"].indexOf(o["list-style-type"])>-1;o["list-style"]=t?"unordered":"ordered"}else o["list-style"]="none";const i=ut.ancestor(t.sc,ut.isPara);if(i&&i.style["line-height"])o["line-height"]=i.style.lineHeight;else{const t=parseInt(o["line-height"],10)/parseInt(o["font-size"],10);o["line-height"]=t.toFixed(1)}return o.anchor=t.isOnAnchor()&&ut.ancestor(t.sc,ut.isAnchor),o.ancestors=ut.listAncestor(t.sc,ut.isEditable),o.range=t,o}}class yt{insertOrderedList(t){this.toggleList("OL",t)}insertUnorderedList(t){this.toggleList("UL",t)}indent(t){const e=bt.create(t).wrapBodyInlineWithPara(),o=e.nodes(ut.isPara,{includeAncestor:!0}),i=y.clusterBy(o,b.peq2("parentNode"));n.a.each(i,(t,e)=>{const o=y.head(e);if(ut.isLi(o)){const t=this.findList(o.previousSibling);t?e.map(e=>t.appendChild(e)):(this.wrapList(e,o.parentNode.nodeName),e.map(t=>t.parentNode).map(t=>this.appendToPrevious(t)))}else n.a.each(e,(t,e)=>{n()(e).css("marginLeft",(t,e)=>(parseInt(e,10)||0)+25)})}),e.select()}outdent(t){const e=bt.create(t).wrapBodyInlineWithPara(),o=e.nodes(ut.isPara,{includeAncestor:!0}),i=y.clusterBy(o,b.peq2("parentNode"));n.a.each(i,(t,e)=>{const o=y.head(e);ut.isLi(o)?this.releaseList([e]):n.a.each(e,(t,e)=>{n()(e).css("marginLeft",(t,e)=>(e=parseInt(e,10)||0)>25?e-25:"")})}),e.select()}toggleList(t,e){const o=bt.create(e).wrapBodyInlineWithPara();let i=o.nodes(ut.isPara,{includeAncestor:!0});const s=o.paraBookmark(i),r=y.clusterBy(i,b.peq2("parentNode"));if(y.find(i,ut.isPurePara)){let e=[];n.a.each(r,(o,i)=>{e=e.concat(this.wrapList(i,t))}),i=e}else{const e=o.nodes(ut.isList,{includeAncestor:!0}).filter(e=>!n.a.nodeName(e,t));e.length?n.a.each(e,(e,o)=>{ut.replace(o,t)}):i=this.releaseList(r,!0)}bt.createFromParaBookmark(s,i).select()}wrapList(t,e){const o=y.head(t),i=y.last(t),n=ut.isList(o.previousSibling)&&o.previousSibling,s=ut.isList(i.nextSibling)&&i.nextSibling,r=n||ut.insertAfter(ut.create(e||"UL"),i);return t=t.map(t=>ut.isPurePara(t)?ut.replace(t,"LI"):t),ut.appendChildNodes(r,t),s&&(ut.appendChildNodes(r,y.from(s.childNodes)),ut.remove(s)),t}releaseList(t,e){let o=[];return n.a.each(t,(t,i)=>{const s=y.head(i),r=y.last(i),a=e?ut.lastAncestor(s,ut.isList):s.parentNode,l=a.parentNode;if("LI"===a.parentNode.nodeName)i.map(t=>{const e=this.findNextSiblings(t);l.nextSibling?l.parentNode.insertBefore(t,l.nextSibling):l.parentNode.appendChild(t),e.length&&(this.wrapList(e,a.nodeName),t.appendChild(e[0].parentNode))}),0===a.children.length&&l.removeChild(a),0===l.childNodes.length&&l.parentNode.removeChild(l);else{const t=a.childNodes.length>1?ut.splitTree(a,{node:r.parentNode,offset:ut.position(r)+1},{isSkipPaddingBlankHTML:!0}):null,o=ut.splitTree(a,{node:s.parentNode,offset:ut.position(s)},{isSkipPaddingBlankHTML:!0});i=e?ut.listDescendant(o,ut.isLi):y.from(o.childNodes).filter(ut.isLi),!e&&ut.isList(a.parentNode)||(i=i.map(t=>ut.replace(t,"P"))),n.a.each(y.from(i).reverse(),(t,e)=>{ut.insertAfter(e,a)});const l=y.compact([a,o,t]);n.a.each(l,(t,e)=>{const o=[e].concat(ut.listDescendant(e,ut.isList));n.a.each(o.reverse(),(t,e)=>{ut.nodeLength(e)||ut.remove(e,!0)})})}o=o.concat(i)}),o}appendToPrevious(t){return t.previousSibling?ut.appendChildNodes(t.previousSibling,[t]):this.wrapList([t],"LI")}findList(t){return t?y.find(t.children,t=>["OL","UL"].indexOf(t.nodeName)>-1):null}findNextSiblings(t){const e=[];for(;t.nextSibling;)e.push(t.nextSibling),t=t.nextSibling;return e}}class xt{constructor(t){this.bullet=new yt,this.options=t.options}insertTab(t,e){const o=ut.createText(new Array(e+1).join(ut.NBSP_CHAR));(t=t.deleteContents()).insertNode(o,!0),(t=bt.create(o,e)).select()}insertParagraph(t,e){e=(e=(e=e||bt.create(t)).deleteContents()).wrapBodyInlineWithPara();const o=ut.ancestor(e.sc,ut.isPara);let i;if(o){if(ut.isLi(o)&&(ut.isEmpty(o)||ut.deepestChildIsEmpty(o)))return void this.bullet.toggleList(o.parentNode.nodeName);{let t=null;if(1===this.options.blockquoteBreakingLevel?t=ut.ancestor(o,ut.isBlockquote):2===this.options.blockquoteBreakingLevel&&(t=ut.lastAncestor(o,ut.isBlockquote)),t){i=n()(ut.emptyPara)[0],ut.isRightEdgePoint(e.getStartPoint())&&ut.isBR(e.sc.nextSibling)&&n()(e.sc.nextSibling).remove();const o=ut.splitTree(t,e.getStartPoint(),{isDiscardEmptySplits:!0});o?o.parentNode.insertBefore(i,o):ut.insertAfter(i,t)}else{i=ut.splitTree(o,e.getStartPoint());let t=ut.listDescendant(o,ut.isEmptyAnchor);t=t.concat(ut.listDescendant(i,ut.isEmptyAnchor)),n.a.each(t,(t,e)=>{ut.remove(e)}),(ut.isHeading(i)||ut.isPre(i)||ut.isCustomStyleTag(i))&&ut.isEmpty(i)&&(i=ut.replace(i,"p"))}}}else{const t=e.sc.childNodes[e.so];i=n()(ut.emptyPara)[0],t?e.sc.insertBefore(i,t):e.sc.appendChild(i)}bt.create(i,0).normalize().select().scrollIntoView(t)}}const St=function(t,e,o,i){const n={colPos:0,rowPos:0},s=[],r=[];function a(t,e,o,i,n,r,a){const l={baseRow:o,baseCell:i,isRowSpan:n,isColSpan:r,isVirtual:a};s[t]||(s[t]=[]),s[t][e]=l}function l(t,e,o,i){return{baseCell:t.baseCell,action:e,virtualTable:{rowIndex:o,cellIndex:i}}}function c(t,e){if(!s[t])return e;if(!s[t][e])return e;let o=e;for(;s[t][o];)if(o++,!s[t][o])return o}function d(t,e){const o=c(t.rowIndex,e.cellIndex),i=e.colSpan>1,s=e.rowSpan>1,r=t.rowIndex===n.rowPos&&e.cellIndex===n.colPos;a(t.rowIndex,o,t,e,s,i,!1);const l=e.attributes.rowSpan?parseInt(e.attributes.rowSpan.value,10):0;if(l>1)for(let n=1;n<l;n++){const s=t.rowIndex+n;h(s,o,e,r),a(s,o,t,e,!0,i,!0)}const d=e.attributes.colSpan?parseInt(e.attributes.colSpan.value,10):0;if(d>1)for(let i=1;i<d;i++){const n=c(t.rowIndex,o+i);h(t.rowIndex,n,e,r),a(t.rowIndex,n,t,e,s,!0,!0)}}function h(t,e,o,i){t===n.rowPos&&n.colPos>=o.cellIndex&&o.cellIndex<=e&&!i&&n.colPos++}function u(t){switch(e){case St.where.Column:if(t.isColSpan)return St.resultAction.SubtractSpanCount;break;case St.where.Row:if(!t.isVirtual&&t.isRowSpan)return St.resultAction.AddCell;if(t.isRowSpan)return St.resultAction.SubtractSpanCount}return St.resultAction.RemoveCell}function p(t){switch(e){case St.where.Column:if(t.isColSpan)return St.resultAction.SumSpanCount;if(t.isRowSpan&&t.isVirtual)return St.resultAction.Ignore;break;case St.where.Row:if(t.isRowSpan)return St.resultAction.SumSpanCount;if(t.isColSpan&&t.isVirtual)return St.resultAction.Ignore}return St.resultAction.AddCell}this.getActionList=function(){const t=e===St.where.Row?n.rowPos:-1,i=e===St.where.Column?n.colPos:-1;let a=0,c=!0;for(;c;){const e=t>=0?t:a,n=i>=0?i:a,d=s[e];if(!d)return c=!1,r;const h=d[n];if(!h)return c=!1,r;let m=St.resultAction.Ignore;switch(o){case St.requestAction.Add:m=p(h);break;case St.requestAction.Delete:m=u(h)}r.push(l(h,m,e,n)),a++}return r},t&&t.tagName&&("td"===t.tagName.toLowerCase()||"th"===t.tagName.toLowerCase())?(n.colPos=t.cellIndex,t.parentElement&&t.parentElement.tagName&&"tr"===t.parentElement.tagName.toLowerCase()?n.rowPos=t.parentElement.rowIndex:console.error("Impossible to identify start Row point.",t)):console.error("Impossible to identify start Cell point.",t),function(){const t=i.rows;for(let e=0;e<t.length;e++){const o=t[e].cells;for(let i=0;i<o.length;i++)d(t[e],o[i])}}()};St.where={Row:0,Column:1},St.requestAction={Add:0,Delete:1},St.resultAction={Ignore:0,SubtractSpanCount:1,RemoveCell:2,AddCell:3,SumSpanCount:4};class $t{tab(t,e){const o=ut.ancestor(t.commonAncestor(),ut.isCell),i=ut.ancestor(o,ut.isTable),n=ut.listDescendant(i,ut.isCell),s=y[e?"prev":"next"](n,o);s&&bt.create(s,0).select()}addRow(t,e){const o=ut.ancestor(t.commonAncestor(),ut.isCell),i=n()(o).closest("tr"),s=this.recoverAttributes(i),r=n()("<tr"+s+"></tr>"),a=new St(o,St.where.Row,St.requestAction.Add,n()(i).closest("table")[0]).getActionList();for(let t=0;t<a.length;t++){const o=a[t],s=this.recoverAttributes(o.baseCell);switch(o.action){case St.resultAction.AddCell:r.append("<td"+s+">"+ut.blank+"</td>");break;case St.resultAction.SumSpanCount:if("top"===e){if((o.baseCell.parent?o.baseCell.closest("tr").rowIndex:0)<=i[0].rowIndex){const t=n()("<div></div>").append(n()("<td"+s+">"+ut.blank+"</td>").removeAttr("rowspan")).html();r.append(t);break}}let t=parseInt(o.baseCell.rowSpan,10);t++,o.baseCell.setAttribute("rowSpan",t)}}if("top"===e)i.before(r);else{if(o.rowSpan>1){const t=i[0].rowIndex+(o.rowSpan-2);return void n()(n()(i).parent().find("tr")[t]).after(n()(r))}i.after(r)}}addCol(t,e){const o=ut.ancestor(t.commonAncestor(),ut.isCell),i=n()(o).closest("tr");n()(i).siblings().push(i);const s=new St(o,St.where.Column,St.requestAction.Add,n()(i).closest("table")[0]).getActionList();for(let t=0;t<s.length;t++){const o=s[t],i=this.recoverAttributes(o.baseCell);switch(o.action){case St.resultAction.AddCell:"right"===e?n()(o.baseCell).after("<td"+i+">"+ut.blank+"</td>"):n()(o.baseCell).before("<td"+i+">"+ut.blank+"</td>");break;case St.resultAction.SumSpanCount:if("right"===e){let t=parseInt(o.baseCell.colSpan,10);t++,o.baseCell.setAttribute("colSpan",t)}else n()(o.baseCell).before("<td"+i+">"+ut.blank+"</td>")}}}recoverAttributes(t){let e="";if(!t)return e;const o=t.attributes||[];for(let t=0;t<o.length;t++)"id"!==o[t].name.toLowerCase()&&o[t].specified&&(e+=" "+o[t].name+"='"+o[t].value+"'");return e}deleteRow(t){const e=ut.ancestor(t.commonAncestor(),ut.isCell),o=n()(e).closest("tr"),i=o.children("td, th").index(n()(e)),s=o[0].rowIndex,r=new St(e,St.where.Row,St.requestAction.Delete,n()(o).closest("table")[0]).getActionList();for(let t=0;t<r.length;t++){if(!r[t])continue;const e=r[t].baseCell,n=r[t].virtualTable,a=e.rowSpan&&e.rowSpan>1;let l=a?parseInt(e.rowSpan,10):0;switch(r[t].action){case St.resultAction.Ignore:continue;case St.resultAction.AddCell:const t=o.next("tr")[0];if(!t)continue;const r=o[0].cells[i];a&&(l>2?(l--,t.insertBefore(r,t.cells[i]),t.cells[i].setAttribute("rowSpan",l),t.cells[i].innerHTML=""):2===l&&(t.insertBefore(r,t.cells[i]),t.cells[i].removeAttribute("rowSpan"),t.cells[i].innerHTML=""));continue;case St.resultAction.SubtractSpanCount:a&&(l>2?(l--,e.setAttribute("rowSpan",l),n.rowIndex!==s&&e.cellIndex===i&&(e.innerHTML="")):2===l&&(e.removeAttribute("rowSpan"),n.rowIndex!==s&&e.cellIndex===i&&(e.innerHTML="")));continue;case St.resultAction.RemoveCell:continue}}o.remove()}deleteCol(t){const e=ut.ancestor(t.commonAncestor(),ut.isCell),o=n()(e).closest("tr"),i=o.children("td, th").index(n()(e)),s=new St(e,St.where.Column,St.requestAction.Delete,n()(o).closest("table")[0]).getActionList();for(let t=0;t<s.length;t++)if(s[t])switch(s[t].action){case St.resultAction.Ignore:continue;case St.resultAction.SubtractSpanCount:const e=s[t].baseCell;if(e.colSpan&&e.colSpan>1){let t=e.colSpan?parseInt(e.colSpan,10):0;t>2?(t--,e.setAttribute("colSpan",t),e.cellIndex===i&&(e.innerHTML="")):2===t&&(e.removeAttribute("colSpan"),e.cellIndex===i&&(e.innerHTML=""))}continue;case St.resultAction.RemoveCell:ut.remove(s[t].baseCell,!0);continue}}createTable(t,e,o){const i=[];let s;for(let e=0;e<t;e++)i.push("<td>"+ut.blank+"</td>");s=i.join("");const r=[];let a;for(let t=0;t<e;t++)r.push("<tr>"+s+"</tr>");a=r.join("");const l=n()("<table>"+a+"</table>");return o&&o.tableClassName&&l.addClass(o.tableClassName),l[0]}deleteTable(t){const e=ut.ancestor(t.commonAncestor(),ut.isCell);n()(e).closest("table").remove()}}let It;f.hasCodeMirror&&(It=window.CodeMirror);const Nt=/^([A-Za-z][A-Za-z0-9+-.]*\:[\/]{2}|tel:|mailto:[A-Z0-9._%+-]+@)?(www\.)?(.+)$/i;n.a.summernote=n.a.extend(n.a.summernote,{version:"0.8.15",plugins:{},dom:ut,range:bt,lists:y,options:{langInfo:n.a.summernote.lang["en-US"],editing:!0,modules:{editor:class{constructor(t){this.context=t,this.$note=t.layoutInfo.note,this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.options=t.options,this.lang=this.options.langInfo,this.editable=this.$editable[0],this.lastRange=null,this.snapshot=null,this.style=new wt,this.table=new $t,this.typing=new xt(t),this.bullet=new yt,this.history=new Ct(this.$editable),this.context.memo("help.undo",this.lang.help.undo),this.context.memo("help.redo",this.lang.help.redo),this.context.memo("help.tab",this.lang.help.tab),this.context.memo("help.untab",this.lang.help.untab),this.context.memo("help.insertParagraph",this.lang.help.insertParagraph),this.context.memo("help.insertOrderedList",this.lang.help.insertOrderedList),this.context.memo("help.insertUnorderedList",this.lang.help.insertUnorderedList),this.context.memo("help.indent",this.lang.help.indent),this.context.memo("help.outdent",this.lang.help.outdent),this.context.memo("help.formatPara",this.lang.help.formatPara),this.context.memo("help.insertHorizontalRule",this.lang.help.insertHorizontalRule),this.context.memo("help.fontName",this.lang.help.fontName);const e=["bold","italic","underline","strikethrough","superscript","subscript","justifyLeft","justifyCenter","justifyRight","justifyFull","formatBlock","removeFormat","backColor"];for(let t=0,o=e.length;t<o;t++)this[e[t]]=(t=>e=>{this.beforeCommand(),document.execCommand(t,!1,e),this.afterCommand(!0)})(e[t]),this.context.memo("help."+e[t],this.lang.help[e[t]]);this.fontName=this.wrapCommand(t=>this.fontStyling("font-family",f.validFontName(t))),this.fontSize=this.wrapCommand(t=>{const e=this.currentStyle()["font-size-unit"];return this.fontStyling("font-size",t+e)}),this.fontSizeUnit=this.wrapCommand(t=>{const e=this.currentStyle()["font-size"];return this.fontStyling("font-size",e+t)});for(let t=1;t<=6;t++)this["formatH"+t]=(t=>()=>{this.formatBlock("H"+t)})(t),this.context.memo("help.formatH"+t,this.lang.help["formatH"+t]);this.insertParagraph=this.wrapCommand(()=>{this.typing.insertParagraph(this.editable)}),this.insertOrderedList=this.wrapCommand(()=>{this.bullet.insertOrderedList(this.editable)}),this.insertUnorderedList=this.wrapCommand(()=>{this.bullet.insertUnorderedList(this.editable)}),this.indent=this.wrapCommand(()=>{this.bullet.indent(this.editable)}),this.outdent=this.wrapCommand(()=>{this.bullet.outdent(this.editable)}),this.insertNode=this.wrapCommand(t=>{if(this.isLimited(n()(t).text().length))return;this.getLastRange().insertNode(t),this.setLastRange(bt.createFromNodeAfter(t).select())}),this.insertText=this.wrapCommand(t=>{if(this.isLimited(t.length))return;const e=this.getLastRange().insertNode(ut.createText(t));this.setLastRange(bt.create(e,ut.nodeLength(e)).select())}),this.pasteHTML=this.wrapCommand(t=>{if(this.isLimited(t.length))return;t=this.context.invoke("codeview.purify",t);const e=this.getLastRange().pasteHTML(t);this.setLastRange(bt.createFromNodeAfter(y.last(e)).select())}),this.formatBlock=this.wrapCommand((t,e)=>{const o=this.options.callbacks.onApplyCustomStyle;o?o.call(this,e,this.context,this.onFormatBlock):this.onFormatBlock(t,e)}),this.insertHorizontalRule=this.wrapCommand(()=>{const t=this.getLastRange().insertNode(ut.create("HR"));t.nextSibling&&this.setLastRange(bt.create(t.nextSibling,0).normalize().select())}),this.lineHeight=this.wrapCommand(t=>{this.style.stylePara(this.getLastRange(),{lineHeight:t})}),this.createLink=this.wrapCommand(t=>{let e=t.url;const o=t.text,i=t.isNewWindow,s=t.checkProtocol;let r=t.range||this.getLastRange();const a=o.length-r.toString().length;if(a>0&&this.isLimited(a))return;const l=r.toString()!==o;"string"==typeof e&&(e=e.trim()),this.options.onCreateLink?e=this.options.onCreateLink(e):s&&(e=/^([A-Za-z][A-Za-z0-9+-.]*\:|#|\/)/.test(e)?e:this.options.defaultProtocol+e);let c=[];if(l){r=r.deleteContents();const t=r.insertNode(n()("<A>"+o+"</A>")[0]);c.push(t)}else c=this.style.styleNodes(r,{nodeName:"A",expandClosestSibling:!0,onlyPartialContains:!0});n.a.each(c,(t,o)=>{n()(o).attr("href",e),i?n()(o).attr("target","_blank"):n()(o).removeAttr("target")});const d=bt.createFromNodeBefore(y.head(c)).getStartPoint(),h=bt.createFromNodeAfter(y.last(c)).getEndPoint();this.setLastRange(bt.create(d.node,d.offset,h.node,h.offset).select())}),this.color=this.wrapCommand(t=>{const e=t.foreColor,o=t.backColor;e&&document.execCommand("foreColor",!1,e),o&&document.execCommand("backColor",!1,o)}),this.foreColor=this.wrapCommand(t=>{document.execCommand("styleWithCSS",!1,!0),document.execCommand("foreColor",!1,t)}),this.insertTable=this.wrapCommand(t=>{const e=t.split("x");this.getLastRange().deleteContents().insertNode(this.table.createTable(e[0],e[1],this.options))}),this.removeMedia=this.wrapCommand(()=>{let t=n()(this.restoreTarget()).parent();t.closest("figure").length?t.closest("figure").remove():t=n()(this.restoreTarget()).detach(),this.context.triggerEvent("media.delete",t,this.$editable)}),this.floatMe=this.wrapCommand(t=>{const e=n()(this.restoreTarget());e.toggleClass("note-float-left","left"===t),e.toggleClass("note-float-right","right"===t),e.css("float","none"===t?"":t)}),this.resize=this.wrapCommand(t=>{const e=n()(this.restoreTarget());0===(t=parseFloat(t))?e.css("width",""):e.css({width:100*t+"%",height:""})})}initialize(){this.$editable.on("keydown",t=>{if(t.keyCode===kt.code.ENTER&&this.context.triggerEvent("enter",t),this.context.triggerEvent("keydown",t),this.snapshot=this.history.makeSnapshot(),t.isDefaultPrevented()||(this.options.shortcuts?this.handleKeyMap(t):this.preventDefaultEditableShortCuts(t)),this.isLimited(1,t)){const t=this.getLastRange();if(t.eo-t.so==0)return!1}this.setLastRange()}).on("keyup",t=>{this.setLastRange(),this.context.triggerEvent("keyup",t)}).on("focus",t=>{this.setLastRange(),this.context.triggerEvent("focus",t)}).on("blur",t=>{this.context.triggerEvent("blur",t)}).on("mousedown",t=>{this.context.triggerEvent("mousedown",t)}).on("mouseup",t=>{this.setLastRange(),this.history.recordUndo(),this.context.triggerEvent("mouseup",t)}).on("scroll",t=>{this.context.triggerEvent("scroll",t)}).on("paste",t=>{this.setLastRange(),this.context.triggerEvent("paste",t)}).on("input",t=>{this.isLimited(0)&&this.snapshot&&this.history.applySnapshot(this.snapshot)}),this.$editable.attr("spellcheck",this.options.spellCheck),this.$editable.attr("autocorrect",this.options.spellCheck),this.options.disableGrammar&&this.$editable.attr("data-gramm",!1),this.$editable.html(ut.html(this.$note)||ut.emptyPara),this.$editable.on(f.inputEventName,b.debounce(()=>{this.context.triggerEvent("change",this.$editable.html(),this.$editable)},10)),this.$editor.on("focusin",t=>{this.context.triggerEvent("focusin",t)}).on("focusout",t=>{this.context.triggerEvent("focusout",t)}),this.options.airMode||(this.options.width&&this.$editor.outerWidth(this.options.width),this.options.height&&this.$editable.outerHeight(this.options.height),this.options.maxHeight&&this.$editable.css("max-height",this.options.maxHeight),this.options.minHeight&&this.$editable.css("min-height",this.options.minHeight)),this.history.recordUndo(),this.setLastRange()}destroy(){this.$editable.off()}handleKeyMap(t){const e=this.options.keyMap[f.isMac?"mac":"pc"],o=[];t.metaKey&&o.push("CMD"),t.ctrlKey&&!t.altKey&&o.push("CTRL"),t.shiftKey&&o.push("SHIFT");const i=kt.nameFromCode[t.keyCode];i&&o.push(i);const n=e[o.join("+")];"TAB"!==i||this.options.tabDisable?n?!1!==this.context.invoke(n)&&t.preventDefault():kt.isEdit(t.keyCode)&&this.afterCommand():this.afterCommand()}preventDefaultEditableShortCuts(t){(t.ctrlKey||t.metaKey)&&y.contains([66,73,85],t.keyCode)&&t.preventDefault()}isLimited(t,e){return t=t||0,(void 0===e||!(kt.isMove(e.keyCode)||kt.isNavigation(e.keyCode)||e.ctrlKey||e.metaKey||y.contains([kt.code.BACKSPACE,kt.code.DELETE],e.keyCode)))&&(this.options.maxTextLength>0&&this.$editable.text().length+t>this.options.maxTextLength)}createRange(){return this.focus(),this.setLastRange(),this.getLastRange()}setLastRange(t){t?this.lastRange=t:(this.lastRange=bt.create(this.editable),0===n()(this.lastRange.sc).closest(".note-editable").length&&(this.lastRange=bt.createFromBodyElement(this.editable)))}getLastRange(){return this.lastRange||this.setLastRange(),this.lastRange}saveRange(t){t&&this.getLastRange().collapse().select()}restoreRange(){this.lastRange&&(this.lastRange.select(),this.focus())}saveTarget(t){this.$editable.data("target",t)}clearTarget(){this.$editable.removeData("target")}restoreTarget(){return this.$editable.data("target")}currentStyle(){let t=bt.create();return t&&(t=t.normalize()),t?this.style.current(t):this.style.fromNode(this.$editable)}styleFromNode(t){return this.style.fromNode(t)}undo(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.undo(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}commit(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.commit(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}redo(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.redo(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}beforeCommand(){this.context.triggerEvent("before.command",this.$editable.html()),this.focus()}afterCommand(t){this.normalizeContent(),this.history.recordUndo(),t||this.context.triggerEvent("change",this.$editable.html(),this.$editable)}tab(){const t=this.getLastRange();if(t.isCollapsed()&&t.isOnCell())this.table.tab(t);else{if(0===this.options.tabSize)return!1;this.isLimited(this.options.tabSize)||(this.beforeCommand(),this.typing.insertTab(t,this.options.tabSize),this.afterCommand())}}untab(){const t=this.getLastRange();if(t.isCollapsed()&&t.isOnCell())this.table.tab(t,!0);else if(0===this.options.tabSize)return!1}wrapCommand(t){return function(){this.beforeCommand(),t.apply(this,arguments),this.afterCommand()}}insertImage(t,e){return(o=t,n.a.Deferred(t=>{const e=n()("<img>");e.one("load",()=>{e.off("error abort"),t.resolve(e)}).one("error abort",()=>{e.off("load").detach(),t.reject(e)}).css({display:"none"}).appendTo(document.body).attr("src",o)}).promise()).then(t=>{this.beforeCommand(),"function"==typeof e?e(t):("string"==typeof e&&t.attr("data-filename",e),t.css("width",Math.min(this.$editable.width(),t.width()))),t.show(),this.getLastRange().insertNode(t[0]),this.setLastRange(bt.createFromNodeAfter(t[0]).select()),this.afterCommand()}).fail(t=>{this.context.triggerEvent("image.upload.error",t)});var o}insertImagesAsDataURL(t){n.a.each(t,(t,e)=>{const o=e.name;this.options.maximumImageFileSize&&this.options.maximumImageFileSize<e.size?this.context.triggerEvent("image.upload.error",this.lang.image.maximumFileSizeError):function(t){return n.a.Deferred(e=>{n.a.extend(new FileReader,{onload:t=>{const o=t.target.result;e.resolve(o)},onerror:t=>{e.reject(t)}}).readAsDataURL(t)}).promise()}(e).then(t=>this.insertImage(t,o)).fail(()=>{this.context.triggerEvent("image.upload.error")})})}insertImagesOrCallback(t){this.options.callbacks.onImageUpload?this.context.triggerEvent("image.upload",t):this.insertImagesAsDataURL(t)}getSelectedText(){let t=this.getLastRange();return t.isOnAnchor()&&(t=bt.createFromNode(ut.ancestor(t.sc,ut.isAnchor))),t.toString()}onFormatBlock(t,e){if(document.execCommand("FormatBlock",!1,f.isMSIE?"<"+t+">":t),e&&e.length&&(e[0].tagName.toUpperCase()!==t.toUpperCase()&&(e=e.find(t)),e&&e.length)){const o=e[0].className||"";if(o){const e=this.createRange();n()([e.sc,e.ec]).closest(t).addClass(o)}}}formatPara(){this.formatBlock("P")}fontStyling(t,e){const o=this.getLastRange();if(""!==o){const i=this.style.styleNodes(o);if(this.$editor.find(".note-status-output").html(""),n()(i).css(t,e),o.isCollapsed()){const t=y.head(i);t&&!ut.nodeLength(t)&&(t.innerHTML=ut.ZERO_WIDTH_NBSP_CHAR,bt.createFromNodeAfter(t.firstChild).select(),this.setLastRange(),this.$editable.data("bogus",t))}}else{const t=n.a.now();this.$editor.find(".note-status-output").html('<div id="note-status-output-'+t+'" class="alert alert-info">'+this.lang.output.noSelection+"</div>"),setTimeout((function(){n()("#note-status-output-"+t).remove()}),5e3)}}unlink(){let t=this.getLastRange();if(t.isOnAnchor()){const e=ut.ancestor(t.sc,ut.isAnchor);t=bt.createFromNode(e),t.select(),this.setLastRange(),this.beforeCommand(),document.execCommand("unlink"),this.afterCommand()}}getLinkInfo(){const t=this.getLastRange().expand(ut.isAnchor),e=n()(y.head(t.nodes(ut.isAnchor))),o={range:t,text:t.toString(),url:e.length?e.attr("href"):""};return e.length&&(o.isNewWindow="_blank"===e.attr("target")),o}addRow(t){const e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.addRow(e,t),this.afterCommand())}addCol(t){const e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.addCol(e,t),this.afterCommand())}deleteRow(){const t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteRow(t),this.afterCommand())}deleteCol(){const t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteCol(t),this.afterCommand())}deleteTable(){const t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteTable(t),this.afterCommand())}resizeTo(t,e,o){let i;if(o){const o=t.y/t.x,n=e.data("ratio");i={width:n>o?t.x:t.y/n,height:n>o?t.x*n:t.y}}else i={width:t.x,height:t.y};e.css(i)}hasFocus(){return this.$editable.is(":focus")}focus(){this.hasFocus()||this.$editable.focus()}isEmpty(){return ut.isEmpty(this.$editable[0])||ut.emptyPara===this.$editable.html()}empty(){this.context.invoke("code",ut.emptyPara)}normalizeContent(){this.$editable[0].normalize()}},clipboard:class{constructor(t){this.context=t,this.$editable=t.layoutInfo.editable}initialize(){this.$editable.on("paste",this.pasteByEvent.bind(this))}pasteByEvent(t){const e=t.originalEvent.clipboardData;if(e&&e.items&&e.items.length){const o=e.items.length>1?e.items[1]:y.head(e.items);"file"===o.kind&&-1!==o.type.indexOf("image/")?(this.context.invoke("editor.insertImagesOrCallback",[o.getAsFile()]),t.preventDefault(),this.context.invoke("editor.afterCommand")):"string"===o.kind&&(this.context.invoke("editor.isLimited",e.getData("Text").length)?t.preventDefault():this.context.invoke("editor.afterCommand"))}}},dropzone:class{constructor(t){this.context=t,this.$eventListener=n()(document),this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.options=t.options,this.lang=this.options.langInfo,this.documentEventHandlers={},this.$dropzone=n()(['<div class="note-dropzone">','<div class="note-dropzone-message"/>',"</div>"].join("")).prependTo(this.$editor)}initialize(){this.options.disableDragAndDrop?(this.documentEventHandlers.onDrop=t=>{t.preventDefault()},this.$eventListener=this.$dropzone,this.$eventListener.on("drop",this.documentEventHandlers.onDrop)):this.attachDragAndDropEvent()}attachDragAndDropEvent(){let t=n()();const e=this.$dropzone.find(".note-dropzone-message");this.documentEventHandlers.onDragenter=o=>{const i=this.context.invoke("codeview.isActivated"),n=this.$editor.width()>0&&this.$editor.height()>0;i||t.length||!n||(this.$editor.addClass("dragover"),this.$dropzone.width(this.$editor.width()),this.$dropzone.height(this.$editor.height()),e.text(this.lang.image.dragImageHere)),t=t.add(o.target)},this.documentEventHandlers.onDragleave=e=>{t=t.not(e.target),t.length&&"BODY"!==e.target.nodeName||(t=n()(),this.$editor.removeClass("dragover"))},this.documentEventHandlers.onDrop=()=>{t=n()(),this.$editor.removeClass("dragover")},this.$eventListener.on("dragenter",this.documentEventHandlers.onDragenter).on("dragleave",this.documentEventHandlers.onDragleave).on("drop",this.documentEventHandlers.onDrop),this.$dropzone.on("dragenter",()=>{this.$dropzone.addClass("hover"),e.text(this.lang.image.dropImage)}).on("dragleave",()=>{this.$dropzone.removeClass("hover"),e.text(this.lang.image.dragImageHere)}),this.$dropzone.on("drop",t=>{const e=t.originalEvent.dataTransfer;t.preventDefault(),e&&e.files&&e.files.length?(this.$editable.focus(),this.context.invoke("editor.insertImagesOrCallback",e.files)):n.a.each(e.types,(t,o)=>{if(o.toLowerCase().indexOf("_moz_")>-1)return;const i=e.getData(o);o.toLowerCase().indexOf("text")>-1?this.context.invoke("editor.pasteHTML",i):n()(i).each((t,e)=>{this.context.invoke("editor.insertNode",e)})})}).on("dragover",!1)}destroy(){Object.keys(this.documentEventHandlers).forEach(t=>{this.$eventListener.off(t.substr(2).toLowerCase(),this.documentEventHandlers[t])}),this.documentEventHandlers={}}},codeview:class{constructor(t){this.context=t,this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.$codable=t.layoutInfo.codable,this.options=t.options}sync(){this.isActivated()&&f.hasCodeMirror&&this.$codable.data("cmEditor").save()}isActivated(){return this.$editor.hasClass("codeview")}toggle(){this.isActivated()?this.deactivate():this.activate(),this.context.triggerEvent("codeview.toggled")}purify(t){if(this.options.codeviewFilter&&(t=t.replace(this.options.codeviewFilterRegex,""),this.options.codeviewIframeFilter)){const e=this.options.codeviewIframeWhitelistSrc.concat(this.options.codeviewIframeWhitelistSrcBase);t=t.replace(/(<iframe.*?>.*?(?:<\/iframe>)?)/gi,(function(t){if(/<.+src(?==?('|"|\s)?)[\s\S]+src(?=('|"|\s)?)[^>]*?>/i.test(t))return"";for(const o of e)if(new RegExp('src="(https?:)?//'+o.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")+'/(.+)"').test(t))return t;return""}))}return t}activate(){if(this.$codable.val(ut.html(this.$editable,this.options.prettifyHtml)),this.$codable.height(this.$editable.height()),this.context.invoke("toolbar.updateCodeview",!0),this.$editor.addClass("codeview"),this.$codable.focus(),f.hasCodeMirror){const t=It.fromTextArea(this.$codable[0],this.options.codemirror);if(this.options.codemirror.tern){const e=new It.TernServer(this.options.codemirror.tern);t.ternServer=e,t.on("cursorActivity",t=>{e.updateArgHints(t)})}t.on("blur",e=>{this.context.triggerEvent("blur.codeview",t.getValue(),e)}),t.on("change",e=>{this.context.triggerEvent("change.codeview",t.getValue(),t)}),t.setSize(null,this.$editable.outerHeight()),this.$codable.data("cmEditor",t)}else this.$codable.on("blur",t=>{this.context.triggerEvent("blur.codeview",this.$codable.val(),t)}),this.$codable.on("input",t=>{this.context.triggerEvent("change.codeview",this.$codable.val(),this.$codable)})}deactivate(){if(f.hasCodeMirror){const t=this.$codable.data("cmEditor");this.$codable.val(t.getValue()),t.toTextArea()}const t=this.purify(ut.value(this.$codable,this.options.prettifyHtml)||ut.emptyPara),e=this.$editable.html()!==t;this.$editable.html(t),this.$editable.height(this.options.height?this.$codable.height():"auto"),this.$editor.removeClass("codeview"),e&&this.context.triggerEvent("change",this.$editable.html(),this.$editable),this.$editable.focus(),this.context.invoke("toolbar.updateCodeview",!1)}destroy(){this.isActivated()&&this.deactivate()}},statusbar:class{constructor(t){this.$document=n()(document),this.$statusbar=t.layoutInfo.statusbar,this.$editable=t.layoutInfo.editable,this.options=t.options}initialize(){this.options.airMode||this.options.disableResizeEditor?this.destroy():this.$statusbar.on("mousedown",t=>{t.preventDefault(),t.stopPropagation();const e=this.$editable.offset().top-this.$document.scrollTop(),o=t=>{let o=t.clientY-(e+24);o=this.options.minheight>0?Math.max(o,this.options.minheight):o,o=this.options.maxHeight>0?Math.min(o,this.options.maxHeight):o,this.$editable.height(o)};this.$document.on("mousemove",o).one("mouseup",()=>{this.$document.off("mousemove",o)})})}destroy(){this.$statusbar.off(),this.$statusbar.addClass("locked")}},fullscreen:class{constructor(t){this.context=t,this.$editor=t.layoutInfo.editor,this.$toolbar=t.layoutInfo.toolbar,this.$editable=t.layoutInfo.editable,this.$codable=t.layoutInfo.codable,this.$window=n()(window),this.$scrollbar=n()("html, body"),this.onResize=()=>{this.resizeTo({h:this.$window.height()-this.$toolbar.outerHeight()})}}resizeTo(t){this.$editable.css("height",t.h),this.$codable.css("height",t.h),this.$codable.data("cmeditor")&&this.$codable.data("cmeditor").setsize(null,t.h)}toggle(){this.$editor.toggleClass("fullscreen"),this.isFullscreen()?(this.$editable.data("orgHeight",this.$editable.css("height")),this.$editable.data("orgMaxHeight",this.$editable.css("maxHeight")),this.$editable.css("maxHeight",""),this.$window.on("resize",this.onResize).trigger("resize"),this.$scrollbar.css("overflow","hidden")):(this.$window.off("resize",this.onResize),this.resizeTo({h:this.$editable.data("orgHeight")}),this.$editable.css("maxHeight",this.$editable.css("orgMaxHeight")),this.$scrollbar.css("overflow","visible")),this.context.invoke("toolbar.updateFullscreen",this.isFullscreen())}isFullscreen(){return this.$editor.hasClass("fullscreen")}},handle:class{constructor(t){this.context=t,this.$document=n()(document),this.$editingArea=t.layoutInfo.editingArea,this.options=t.options,this.lang=this.options.langInfo,this.events={"summernote.mousedown":(t,e)=>{this.update(e.target,e)&&e.preventDefault()},"summernote.keyup summernote.scroll summernote.change summernote.dialog.shown":()=>{this.update()},"summernote.disable summernote.blur":()=>{this.hide()},"summernote.codeview.toggled":()=>{this.update()}}}initialize(){this.$handle=n()(['<div class="note-handle">','<div class="note-control-selection">','<div class="note-control-selection-bg"></div>','<div class="note-control-holder note-control-nw"></div>','<div class="note-control-holder note-control-ne"></div>','<div class="note-control-holder note-control-sw"></div>','<div class="',this.options.disableResizeImage?"note-control-holder":"note-control-sizing",' note-control-se"></div>',this.options.disableResizeImage?"":'<div class="note-control-selection-info"></div>',"</div>","</div>"].join("")).prependTo(this.$editingArea),this.$handle.on("mousedown",t=>{if(ut.isControlSizing(t.target)){t.preventDefault(),t.stopPropagation();const e=this.$handle.find(".note-control-selection").data("target"),o=e.offset(),i=this.$document.scrollTop(),n=t=>{this.context.invoke("editor.resizeTo",{x:t.clientX-o.left,y:t.clientY-(o.top-i)},e,!t.shiftKey),this.update(e[0],t)};this.$document.on("mousemove",n).one("mouseup",t=>{t.preventDefault(),this.$document.off("mousemove",n),this.context.invoke("editor.afterCommand")}),e.data("ratio")||e.data("ratio",e.height()/e.width())}}),this.$handle.on("wheel",t=>{t.preventDefault(),this.update()})}destroy(){this.$handle.remove()}update(t,e){if(this.context.isDisabled())return!1;const o=ut.isImg(t),i=this.$handle.find(".note-control-selection");if(this.context.invoke("imagePopover.update",t,e),o){const e=n()(t),o=e.position(),s={left:o.left+parseInt(e.css("marginLeft"),10),top:o.top+parseInt(e.css("marginTop"),10)},r={w:e.outerWidth(!1),h:e.outerHeight(!1)};i.css({display:"block",left:s.left,top:s.top,width:r.w,height:r.h}).data("target",e);const a=new Image;a.src=e.attr("src");const l=r.w+"x"+r.h+" ("+this.lang.image.original+": "+a.width+"x"+a.height+")";i.find(".note-control-selection-info").text(l),this.context.invoke("editor.saveTarget",t)}else this.hide();return o}hide(){this.context.invoke("editor.clearTarget"),this.$handle.children().hide()}},hintPopover:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.$editable=t.layoutInfo.editable,this.options=t.options,this.hint=this.options.hint||[],this.direction=this.options.hintDirection||"bottom",this.hints=Array.isArray(this.hint)?this.hint:[this.hint],this.events={"summernote.keyup":(t,e)=>{e.isDefaultPrevented()||this.handleKeyup(e)},"summernote.keydown":(t,e)=>{this.handleKeydown(e)},"summernote.disable summernote.dialog.shown summernote.blur":()=>{this.hide()}}}shouldInitialize(){return this.hints.length>0}initialize(){this.lastWordRange=null,this.matchingWord=null,this.$popover=this.ui.popover({className:"note-hint-popover",hideArrow:!0,direction:""}).render().appendTo(this.options.container),this.$popover.hide(),this.$content=this.$popover.find(".popover-content,.note-popover-content"),this.$content.on("click",".note-hint-item",t=>{this.$content.find(".active").removeClass("active"),n()(t.currentTarget).addClass("active"),this.replace()}),this.$popover.on("mousedown",t=>{t.preventDefault()})}destroy(){this.$popover.remove()}selectItem(t){this.$content.find(".active").removeClass("active"),t.addClass("active"),this.$content[0].scrollTop=t[0].offsetTop-this.$content.innerHeight()/2}moveDown(){const t=this.$content.find(".note-hint-item.active"),e=t.next();if(e.length)this.selectItem(e);else{let e=t.parent().next();e.length||(e=this.$content.find(".note-hint-group").first()),this.selectItem(e.find(".note-hint-item").first())}}moveUp(){const t=this.$content.find(".note-hint-item.active"),e=t.prev();if(e.length)this.selectItem(e);else{let e=t.parent().prev();e.length||(e=this.$content.find(".note-hint-group").last()),this.selectItem(e.find(".note-hint-item").last())}}replace(){const t=this.$content.find(".note-hint-item.active");if(t.length){var e=this.nodeFromItem(t);if(null!==this.matchingWord&&0===this.matchingWord.length)this.lastWordRange.so=this.lastWordRange.eo;else if(null!==this.matchingWord&&this.matchingWord.length>0&&!this.lastWordRange.isCollapsed()){let t=this.lastWordRange.eo-this.lastWordRange.so-this.matchingWord.length;t>0&&(this.lastWordRange.so+=t)}if(this.lastWordRange.insertNode(e),"next"===this.options.hintSelect){var o=document.createTextNode("");n()(e).after(o),bt.createFromNodeBefore(o).select()}else bt.createFromNodeAfter(e).select();this.lastWordRange=null,this.hide(),this.context.invoke("editor.focus")}}nodeFromItem(t){const e=this.hints[t.data("index")],o=t.data("item");let i=e.content?e.content(o):o;return"string"==typeof i&&(i=ut.createText(i)),i}createItemTemplates(t,e){const o=this.hints[t];return e.map((e,i)=>{const s=n()('<div class="note-hint-item"/>');return s.append(o.template?o.template(e):e+""),s.data({index:t,item:e}),s})}handleKeydown(t){this.$popover.is(":visible")&&(t.keyCode===kt.code.ENTER?(t.preventDefault(),this.replace()):t.keyCode===kt.code.UP?(t.preventDefault(),this.moveUp()):t.keyCode===kt.code.DOWN&&(t.preventDefault(),this.moveDown()))}searchKeyword(t,e,o){const i=this.hints[t];if(i&&i.match.test(e)&&i.search){const t=i.match.exec(e);this.matchingWord=t[0],i.search(t[1],o)}else o()}createGroup(t,e){const o=n()('<div class="note-hint-group note-hint-group-'+t+'"/>');return this.searchKeyword(t,e,e=>{(e=e||[]).length&&(o.html(this.createItemTemplates(t,e)),this.show())}),o}handleKeyup(t){if(!y.contains([kt.code.ENTER,kt.code.UP,kt.code.DOWN],t.keyCode)){let t,e,o=this.context.invoke("editor.getLastRange");if("words"===this.options.hintMode){if(t=o.getWordsRange(o),e=t.toString(),this.hints.forEach(i=>{if(i.match.test(e))return t=o.getWordsMatchRange(i.match),!1}),!t)return void this.hide();e=t.toString()}else t=o.getWordRange(),e=t.toString();if(this.hints.length&&e){this.$content.empty();const o=b.rect2bnd(y.last(t.getClientRects())),i=n()(this.options.container).offset();o&&(o.top-=i.top,o.left-=i.left,this.$popover.hide(),this.lastWordRange=t,this.hints.forEach((t,o)=>{t.match.test(e)&&this.createGroup(o,e).appendTo(this.$content)}),this.$content.find(".note-hint-item:first").addClass("active"),"top"===this.direction?this.$popover.css({left:o.left,top:o.top-this.$popover.outerHeight()-5}):this.$popover.css({left:o.left,top:o.top+o.height+5}))}else this.hide()}}show(){this.$popover.show()}hide(){this.$popover.hide()}},autoLink:class{constructor(t){this.context=t,this.events={"summernote.keyup":(t,e)=>{e.isDefaultPrevented()||this.handleKeyup(e)},"summernote.keydown":(t,e)=>{this.handleKeydown(e)}}}initialize(){this.lastWordRange=null}destroy(){this.lastWordRange=null}replace(){if(!this.lastWordRange)return;const t=this.lastWordRange.toString(),e=t.match(Nt);if(e&&(e[1]||e[2])){const o=e[1]?t:"http://"+t,i=t.replace(/^(?:https?:\/\/)?(?:tel?:?)?(?:mailto?:?)?(?:www\.)?/i,"").split("/")[0],s=n()("<a />").html(i).attr("href",o)[0];this.context.options.linkTargetBlank&&n()(s).attr("target","_blank"),this.lastWordRange.insertNode(s),this.lastWordRange=null,this.context.invoke("editor.focus")}}handleKeydown(t){if(y.contains([kt.code.ENTER,kt.code.SPACE],t.keyCode)){const t=this.context.invoke("editor.createRange").getWordRange();this.lastWordRange=t}}handleKeyup(t){y.contains([kt.code.ENTER,kt.code.SPACE],t.keyCode)&&this.replace()}},autoSync:class{constructor(t){this.$note=t.layoutInfo.note,this.events={"summernote.change":()=>{this.$note.val(t.invoke("code"))}}}shouldInitialize(){return ut.isTextarea(this.$note[0])}},autoReplace:class{constructor(t){this.context=t,this.options=t.options.replace||{},this.keys=[kt.code.ENTER,kt.code.SPACE,kt.code.PERIOD,kt.code.COMMA,kt.code.SEMICOLON,kt.code.SLASH],this.previousKeydownCode=null,this.events={"summernote.keyup":(t,e)=>{e.isDefaultPrevented()||this.handleKeyup(e)},"summernote.keydown":(t,e)=>{this.handleKeydown(e)}}}shouldInitialize(){return!!this.options.match}initialize(){this.lastWord=null}destroy(){this.lastWord=null}replace(){if(!this.lastWord)return;const t=this,e=this.lastWord.toString();this.options.match(e,(function(e){if(e){let o="";if("string"==typeof e?o=ut.createText(e):e instanceof jQuery?o=e[0]:e instanceof Node&&(o=e),!o)return;t.lastWord.insertNode(o),t.lastWord=null,t.context.invoke("editor.focus")}}))}handleKeydown(t){if(this.previousKeydownCode&&y.contains(this.keys,this.previousKeydownCode))this.previousKeydownCode=t.keyCode;else{if(y.contains(this.keys,t.keyCode)){const t=this.context.invoke("editor.createRange").getWordRange();this.lastWord=t}this.previousKeydownCode=t.keyCode}}handleKeyup(t){y.contains(this.keys,t.keyCode)&&this.replace()}},placeholder:class{constructor(t){this.context=t,this.$editingArea=t.layoutInfo.editingArea,this.options=t.options,!0===this.options.inheritPlaceholder&&(this.options.placeholder=this.context.$note.attr("placeholder")||this.options.placeholder),this.events={"summernote.init summernote.change":()=>{this.update()},"summernote.codeview.toggled":()=>{this.update()}}}shouldInitialize(){return!!this.options.placeholder}initialize(){this.$placeholder=n()('<div class="note-placeholder">'),this.$placeholder.on("click",()=>{this.context.invoke("focus")}).html(this.options.placeholder).prependTo(this.$editingArea),this.update()}destroy(){this.$placeholder.remove()}update(){const t=!this.context.invoke("codeview.isActivated")&&this.context.invoke("editor.isEmpty");this.$placeholder.toggle(t)}},buttons:class{constructor(t){this.ui=n.a.summernote.ui,this.context=t,this.$toolbar=t.layoutInfo.toolbar,this.options=t.options,this.lang=this.options.langInfo,this.invertedKeyMap=b.invertObject(this.options.keyMap[f.isMac?"mac":"pc"])}representShortcut(t){let e=this.invertedKeyMap[t];return this.options.shortcuts&&e?(f.isMac&&(e=e.replace("CMD","⌘").replace("SHIFT","⇧")),e=e.replace("BACKSLASH","\\").replace("SLASH","/").replace("LEFTBRACKET","[").replace("RIGHTBRACKET","]")," ("+e+")"):""}button(t){return!this.options.tooltip&&t.tooltip&&delete t.tooltip,t.container=this.options.container,this.ui.button(t)}initialize(){this.addToolbarButtons(),this.addImagePopoverButtons(),this.addLinkPopoverButtons(),this.addTablePopoverButtons(),this.fontInstalledMap={}}destroy(){delete this.fontInstalledMap}isFontInstalled(t){return this.fontInstalledMap.hasOwnProperty(t)||(this.fontInstalledMap[t]=f.isFontInstalled(t)||y.contains(this.options.fontNamesIgnoreCheck,t)),this.fontInstalledMap[t]}isFontDeservedToAdd(t){return""!==(t=t.toLowerCase())&&this.isFontInstalled(t)&&-1===f.genericFontFamilies.indexOf(t)}colorPalette(t,e,o,i){return this.ui.buttonGroup({className:"note-color "+t,children:[this.button({className:"note-current-color-button",contents:this.ui.icon(this.options.icons.font+" note-recent-color"),tooltip:e,click:t=>{const e=n()(t.currentTarget);o&&i?this.context.invoke("editor.color",{backColor:e.attr("data-backColor"),foreColor:e.attr("data-foreColor")}):o?this.context.invoke("editor.color",{backColor:e.attr("data-backColor")}):i&&this.context.invoke("editor.color",{foreColor:e.attr("data-foreColor")})},callback:t=>{const e=t.find(".note-recent-color");o&&(e.css("background-color",this.options.colorButton.backColor),t.attr("data-backColor",this.options.colorButton.backColor)),i?(e.css("color",this.options.colorButton.foreColor),t.attr("data-foreColor",this.options.colorButton.foreColor)):e.css("color","transparent")}}),this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents("",this.options),tooltip:this.lang.color.more,data:{toggle:"dropdown"}}),this.ui.dropdown({items:(o?['<div class="note-palette">','<div class="note-palette-title">'+this.lang.color.background+"</div>","<div>",'<button type="button" class="note-color-reset btn btn-light" data-event="backColor" data-value="inherit">',this.lang.color.transparent,"</button>","</div>",'<div class="note-holder" data-event="backColor"/>',"<div>",'<button type="button" class="note-color-select btn" data-event="openPalette" data-value="backColorPicker">',this.lang.color.cpSelect,"</button>",'<input type="color" id="backColorPicker" class="note-btn note-color-select-btn" value="'+this.options.colorButton.backColor+'" data-event="backColorPalette">',"</div>",'<div class="note-holder-custom" id="backColorPalette" data-event="backColor"/>',"</div>"].join(""):"")+(i?['<div class="note-palette">','<div class="note-palette-title">'+this.lang.color.foreground+"</div>","<div>",'<button type="button" class="note-color-reset btn btn-light" data-event="removeFormat" data-value="foreColor">',this.lang.color.resetToDefault,"</button>","</div>",'<div class="note-holder" data-event="foreColor"/>',"<div>",'<button type="button" class="note-color-select btn" data-event="openPalette" data-value="foreColorPicker">',this.lang.color.cpSelect,"</button>",'<input type="color" id="foreColorPicker" class="note-btn note-color-select-btn" value="'+this.options.colorButton.foreColor+'" data-event="foreColorPalette">',"</div>",'<div class="note-holder-custom" id="foreColorPalette" data-event="foreColor"/>',"</div>"].join(""):""),callback:t=>{t.find(".note-holder").each((t,e)=>{const o=n()(e);o.append(this.ui.palette({colors:this.options.colors,colorsName:this.options.colorsName,eventName:o.data("event"),container:this.options.container,tooltip:this.options.tooltip}).render())});var e=[["#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF"]];t.find(".note-holder-custom").each((t,o)=>{const i=n()(o);i.append(this.ui.palette({colors:e,colorsName:e,eventName:i.data("event"),container:this.options.container,tooltip:this.options.tooltip}).render())}),t.find("input[type=color]").each((e,o)=>{n()(o).change((function(){const e=t.find("#"+n()(this).data("event")).find(".note-color-btn").first(),o=this.value.toUpperCase();e.css("background-color",o).attr("aria-label",o).attr("data-value",o).attr("data-original-title",o),e.click()}))})},click:e=>{e.stopPropagation();const o=n()("."+t).find(".show"),i=n()(e.target),s=i.data("event");let r=i.attr("data-value");if("openPalette"===s){const t=o.find("#"+r),e=n()(o.find("#"+t.data("event")).find(".note-color-row")[0]),i=e.find(".note-color-btn").last().detach(),s=t.val();i.css("background-color",s).attr("aria-label",s).attr("data-value",s).attr("data-original-title",s),e.prepend(i),t.click()}else if(y.contains(["backColor","foreColor"],s)){const t="backColor"===s?"background-color":"color",e=i.closest(".note-color").find(".note-recent-color"),o=i.closest(".note-color").find(".note-current-color-button");e.css(t,r),o.attr("data-"+s,r),this.context.invoke("editor."+s,r)}}})]}).render()}addToolbarButtons(){this.context.memo("button.style",()=>this.ui.buttonGroup([this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.magic),this.options),tooltip:this.lang.style.style,data:{toggle:"dropdown"}}),this.ui.dropdown({className:"dropdown-style",items:this.options.styleTags,title:this.lang.style.style,template:t=>{"string"==typeof t&&(t={tag:t,title:this.lang.style.hasOwnProperty(t)?this.lang.style[t]:t});const e=t.tag,o=t.title;return"<"+e+(t.style?' style="'+t.style+'" ':"")+(t.className?' class="'+t.className+'"':"")+">"+o+"</"+e+">"},click:this.context.createInvokeHandler("editor.formatBlock")})]).render());for(let t=0,e=this.options.styleTags.length;t<e;t++){const e=this.options.styleTags[t];this.context.memo("button.style."+e,()=>this.button({className:"note-btn-style-"+e,contents:'<div data-value="'+e+'">'+e.toUpperCase()+"</div>",tooltip:this.lang.style[e],click:this.context.createInvokeHandler("editor.formatBlock")}).render())}this.context.memo("button.bold",()=>this.button({className:"note-btn-bold",contents:this.ui.icon(this.options.icons.bold),tooltip:this.lang.font.bold+this.representShortcut("bold"),click:this.context.createInvokeHandlerAndUpdateState("editor.bold")}).render()),this.context.memo("button.italic",()=>this.button({className:"note-btn-italic",contents:this.ui.icon(this.options.icons.italic),tooltip:this.lang.font.italic+this.representShortcut("italic"),click:this.context.createInvokeHandlerAndUpdateState("editor.italic")}).render()),this.context.memo("button.underline",()=>this.button({className:"note-btn-underline",contents:this.ui.icon(this.options.icons.underline),tooltip:this.lang.font.underline+this.representShortcut("underline"),click:this.context.createInvokeHandlerAndUpdateState("editor.underline")}).render()),this.context.memo("button.clear",()=>this.button({contents:this.ui.icon(this.options.icons.eraser),tooltip:this.lang.font.clear+this.representShortcut("removeFormat"),click:this.context.createInvokeHandler("editor.removeFormat")}).render()),this.context.memo("button.strikethrough",()=>this.button({className:"note-btn-strikethrough",contents:this.ui.icon(this.options.icons.strikethrough),tooltip:this.lang.font.strikethrough+this.representShortcut("strikethrough"),click:this.context.createInvokeHandlerAndUpdateState("editor.strikethrough")}).render()),this.context.memo("button.superscript",()=>this.button({className:"note-btn-superscript",contents:this.ui.icon(this.options.icons.superscript),tooltip:this.lang.font.superscript,click:this.context.createInvokeHandlerAndUpdateState("editor.superscript")}).render()),this.context.memo("button.subscript",()=>this.button({className:"note-btn-subscript",contents:this.ui.icon(this.options.icons.subscript),tooltip:this.lang.font.subscript,click:this.context.createInvokeHandlerAndUpdateState("editor.subscript")}).render()),this.context.memo("button.fontname",()=>{const t=this.context.invoke("editor.currentStyle");return this.options.addDefaultFonts&&n.a.each(t["font-family"].split(","),(t,e)=>{e=e.trim().replace(/['"]+/g,""),this.isFontDeservedToAdd(e)&&-1===this.options.fontNames.indexOf(e)&&this.options.fontNames.push(e)}),this.ui.buttonGroup([this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents('<span class="note-current-fontname"/>',this.options),tooltip:this.lang.font.name,data:{toggle:"dropdown"}}),this.ui.dropdownCheck({className:"dropdown-fontname",checkClassName:this.options.icons.menuCheck,items:this.options.fontNames.filter(this.isFontInstalled.bind(this)),title:this.lang.font.name,template:t=>'<span style="font-family: '+f.validFontName(t)+'">'+t+"</span>",click:this.context.createInvokeHandlerAndUpdateState("editor.fontName")})]).render()}),this.context.memo("button.fontsize",()=>this.ui.buttonGroup([this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents('<span class="note-current-fontsize"/>',this.options),tooltip:this.lang.font.size,data:{toggle:"dropdown"}}),this.ui.dropdownCheck({className:"dropdown-fontsize",checkClassName:this.options.icons.menuCheck,items:this.options.fontSizes,title:this.lang.font.size,click:this.context.createInvokeHandlerAndUpdateState("editor.fontSize")})]).render()),this.context.memo("button.fontsizeunit",()=>this.ui.buttonGroup([this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents('<span class="note-current-fontsizeunit"/>',this.options),tooltip:this.lang.font.sizeunit,data:{toggle:"dropdown"}}),this.ui.dropdownCheck({className:"dropdown-fontsizeunit",checkClassName:this.options.icons.menuCheck,items:this.options.fontSizeUnits,title:this.lang.font.sizeunit,click:this.context.createInvokeHandlerAndUpdateState("editor.fontSizeUnit")})]).render()),this.context.memo("button.color",()=>this.colorPalette("note-color-all",this.lang.color.recent,!0,!0)),this.context.memo("button.forecolor",()=>this.colorPalette("note-color-fore",this.lang.color.foreground,!1,!0)),this.context.memo("button.backcolor",()=>this.colorPalette("note-color-back",this.lang.color.background,!0,!1)),this.context.memo("button.ul",()=>this.button({contents:this.ui.icon(this.options.icons.unorderedlist),tooltip:this.lang.lists.unordered+this.representShortcut("insertUnorderedList"),click:this.context.createInvokeHandler("editor.insertUnorderedList")}).render()),this.context.memo("button.ol",()=>this.button({contents:this.ui.icon(this.options.icons.orderedlist),tooltip:this.lang.lists.ordered+this.representShortcut("insertOrderedList"),click:this.context.createInvokeHandler("editor.insertOrderedList")}).render());const t=this.button({contents:this.ui.icon(this.options.icons.alignLeft),tooltip:this.lang.paragraph.left+this.representShortcut("justifyLeft"),click:this.context.createInvokeHandler("editor.justifyLeft")}),e=this.button({contents:this.ui.icon(this.options.icons.alignCenter),tooltip:this.lang.paragraph.center+this.representShortcut("justifyCenter"),click:this.context.createInvokeHandler("editor.justifyCenter")}),o=this.button({contents:this.ui.icon(this.options.icons.alignRight),tooltip:this.lang.paragraph.right+this.representShortcut("justifyRight"),click:this.context.createInvokeHandler("editor.justifyRight")}),i=this.button({contents:this.ui.icon(this.options.icons.alignJustify),tooltip:this.lang.paragraph.justify+this.representShortcut("justifyFull"),click:this.context.createInvokeHandler("editor.justifyFull")}),s=this.button({contents:this.ui.icon(this.options.icons.outdent),tooltip:this.lang.paragraph.outdent+this.representShortcut("outdent"),click:this.context.createInvokeHandler("editor.outdent")}),r=this.button({contents:this.ui.icon(this.options.icons.indent),tooltip:this.lang.paragraph.indent+this.representShortcut("indent"),click:this.context.createInvokeHandler("editor.indent")});this.context.memo("button.justifyLeft",b.invoke(t,"render")),this.context.memo("button.justifyCenter",b.invoke(e,"render")),this.context.memo("button.justifyRight",b.invoke(o,"render")),this.context.memo("button.justifyFull",b.invoke(i,"render")),this.context.memo("button.outdent",b.invoke(s,"render")),this.context.memo("button.indent",b.invoke(r,"render")),this.context.memo("button.paragraph",()=>this.ui.buttonGroup([this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.alignLeft),this.options),tooltip:this.lang.paragraph.paragraph,data:{toggle:"dropdown"}}),this.ui.dropdown([this.ui.buttonGroup({className:"note-align",children:[t,e,o,i]}),this.ui.buttonGroup({className:"note-list",children:[s,r]})])]).render()),this.context.memo("button.height",()=>this.ui.buttonGroup([this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.textHeight),this.options),tooltip:this.lang.font.height,data:{toggle:"dropdown"}}),this.ui.dropdownCheck({items:this.options.lineHeights,checkClassName:this.options.icons.menuCheck,className:"dropdown-line-height",title:this.lang.font.height,click:this.context.createInvokeHandler("editor.lineHeight")})]).render()),this.context.memo("button.table",()=>this.ui.buttonGroup([this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.table),this.options),tooltip:this.lang.table.table,data:{toggle:"dropdown"}}),this.ui.dropdown({title:this.lang.table.table,className:"note-table",items:['<div class="note-dimension-picker">','<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"/>','<div class="note-dimension-picker-highlighted"/>','<div class="note-dimension-picker-unhighlighted"/>',"</div>",'<div class="note-dimension-display">1 x 1</div>'].join("")})],{callback:t=>{t.find(".note-dimension-picker-mousecatcher").css({width:this.options.insertTableMaxSize.col+"em",height:this.options.insertTableMaxSize.row+"em"}).mousedown(this.context.createInvokeHandler("editor.insertTable")).on("mousemove",this.tableMoveHandler.bind(this))}}).render()),this.context.memo("button.link",()=>this.button({contents:this.ui.icon(this.options.icons.link),tooltip:this.lang.link.link+this.representShortcut("linkDialog.show"),click:this.context.createInvokeHandler("linkDialog.show")}).render()),this.context.memo("button.picture",()=>this.button({contents:this.ui.icon(this.options.icons.picture),tooltip:this.lang.image.image,click:this.context.createInvokeHandler("imageDialog.show")}).render()),this.context.memo("button.video",()=>this.button({contents:this.ui.icon(this.options.icons.video),tooltip:this.lang.video.video,click:this.context.createInvokeHandler("videoDialog.show")}).render()),this.context.memo("button.hr",()=>this.button({contents:this.ui.icon(this.options.icons.minus),tooltip:this.lang.hr.insert+this.representShortcut("insertHorizontalRule"),click:this.context.createInvokeHandler("editor.insertHorizontalRule")}).render()),this.context.memo("button.fullscreen",()=>this.button({className:"btn-fullscreen",contents:this.ui.icon(this.options.icons.arrowsAlt),tooltip:this.lang.options.fullscreen,click:this.context.createInvokeHandler("fullscreen.toggle")}).render()),this.context.memo("button.codeview",()=>this.button({className:"btn-codeview",contents:this.ui.icon(this.options.icons.code),tooltip:this.lang.options.codeview,click:this.context.createInvokeHandler("codeview.toggle")}).render()),this.context.memo("button.redo",()=>this.button({contents:this.ui.icon(this.options.icons.redo),tooltip:this.lang.history.redo+this.representShortcut("redo"),click:this.context.createInvokeHandler("editor.redo")}).render()),this.context.memo("button.undo",()=>this.button({contents:this.ui.icon(this.options.icons.undo),tooltip:this.lang.history.undo+this.representShortcut("undo"),click:this.context.createInvokeHandler("editor.undo")}).render()),this.context.memo("button.help",()=>this.button({contents:this.ui.icon(this.options.icons.question),tooltip:this.lang.options.help,click:this.context.createInvokeHandler("helpDialog.show")}).render())}addImagePopoverButtons(){this.context.memo("button.resizeFull",()=>this.button({contents:'<span class="note-fontsize-10">100%</span>',tooltip:this.lang.image.resizeFull,click:this.context.createInvokeHandler("editor.resize","1")}).render()),this.context.memo("button.resizeHalf",()=>this.button({contents:'<span class="note-fontsize-10">50%</span>',tooltip:this.lang.image.resizeHalf,click:this.context.createInvokeHandler("editor.resize","0.5")}).render()),this.context.memo("button.resizeQuarter",()=>this.button({contents:'<span class="note-fontsize-10">25%</span>',tooltip:this.lang.image.resizeQuarter,click:this.context.createInvokeHandler("editor.resize","0.25")}).render()),this.context.memo("button.resizeNone",()=>this.button({contents:this.ui.icon(this.options.icons.rollback),tooltip:this.lang.image.resizeNone,click:this.context.createInvokeHandler("editor.resize","0")}).render()),this.context.memo("button.floatLeft",()=>this.button({contents:this.ui.icon(this.options.icons.floatLeft),tooltip:this.lang.image.floatLeft,click:this.context.createInvokeHandler("editor.floatMe","left")}).render()),this.context.memo("button.floatRight",()=>this.button({contents:this.ui.icon(this.options.icons.floatRight),tooltip:this.lang.image.floatRight,click:this.context.createInvokeHandler("editor.floatMe","right")}).render()),this.context.memo("button.floatNone",()=>this.button({contents:this.ui.icon(this.options.icons.rollback),tooltip:this.lang.image.floatNone,click:this.context.createInvokeHandler("editor.floatMe","none")}).render()),this.context.memo("button.removeMedia",()=>this.button({contents:this.ui.icon(this.options.icons.trash),tooltip:this.lang.image.remove,click:this.context.createInvokeHandler("editor.removeMedia")}).render())}addLinkPopoverButtons(){this.context.memo("button.linkDialogShow",()=>this.button({contents:this.ui.icon(this.options.icons.link),tooltip:this.lang.link.edit,click:this.context.createInvokeHandler("linkDialog.show")}).render()),this.context.memo("button.unlink",()=>this.button({contents:this.ui.icon(this.options.icons.unlink),tooltip:this.lang.link.unlink,click:this.context.createInvokeHandler("editor.unlink")}).render())}addTablePopoverButtons(){this.context.memo("button.addRowUp",()=>this.button({className:"btn-md",contents:this.ui.icon(this.options.icons.rowAbove),tooltip:this.lang.table.addRowAbove,click:this.context.createInvokeHandler("editor.addRow","top")}).render()),this.context.memo("button.addRowDown",()=>this.button({className:"btn-md",contents:this.ui.icon(this.options.icons.rowBelow),tooltip:this.lang.table.addRowBelow,click:this.context.createInvokeHandler("editor.addRow","bottom")}).render()),this.context.memo("button.addColLeft",()=>this.button({className:"btn-md",contents:this.ui.icon(this.options.icons.colBefore),tooltip:this.lang.table.addColLeft,click:this.context.createInvokeHandler("editor.addCol","left")}).render()),this.context.memo("button.addColRight",()=>this.button({className:"btn-md",contents:this.ui.icon(this.options.icons.colAfter),tooltip:this.lang.table.addColRight,click:this.context.createInvokeHandler("editor.addCol","right")}).render()),this.context.memo("button.deleteRow",()=>this.button({className:"btn-md",contents:this.ui.icon(this.options.icons.rowRemove),tooltip:this.lang.table.delRow,click:this.context.createInvokeHandler("editor.deleteRow")}).render()),this.context.memo("button.deleteCol",()=>this.button({className:"btn-md",contents:this.ui.icon(this.options.icons.colRemove),tooltip:this.lang.table.delCol,click:this.context.createInvokeHandler("editor.deleteCol")}).render()),this.context.memo("button.deleteTable",()=>this.button({className:"btn-md",contents:this.ui.icon(this.options.icons.trash),tooltip:this.lang.table.delTable,click:this.context.createInvokeHandler("editor.deleteTable")}).render())}build(t,e){for(let o=0,i=e.length;o<i;o++){const i=e[o],n=Array.isArray(i)?i[0]:i,s=Array.isArray(i)?1===i.length?[i[0]]:i[1]:[i],r=this.ui.buttonGroup({className:"note-"+n}).render();for(let t=0,e=s.length;t<e;t++){const e=this.context.memo("button."+s[t]);e&&r.append("function"==typeof e?e(this.context):e)}r.appendTo(t)}}updateCurrentStyle(t){const e=t||this.$toolbar,o=this.context.invoke("editor.currentStyle");if(this.updateBtnStates(e,{".note-btn-bold":()=>"bold"===o["font-bold"],".note-btn-italic":()=>"italic"===o["font-italic"],".note-btn-underline":()=>"underline"===o["font-underline"],".note-btn-subscript":()=>"subscript"===o["font-subscript"],".note-btn-superscript":()=>"superscript"===o["font-superscript"],".note-btn-strikethrough":()=>"strikethrough"===o["font-strikethrough"]}),o["font-family"]){const t=o["font-family"].split(",").map(t=>t.replace(/[\'\"]/g,"").replace(/\s+$/,"").replace(/^\s+/,"")),i=y.find(t,this.isFontInstalled.bind(this));e.find(".dropdown-fontname a").each((t,e)=>{const o=n()(e),s=o.data("value")+""==i+"";o.toggleClass("checked",s)}),e.find(".note-current-fontname").text(i).css("font-family",i)}if(o["font-size"]){const t=o["font-size"];e.find(".dropdown-fontsize a").each((e,o)=>{const i=n()(o),s=i.data("value")+""==t+"";i.toggleClass("checked",s)}),e.find(".note-current-fontsize").text(t);const i=o["font-size-unit"];e.find(".dropdown-fontsizeunit a").each((t,e)=>{const o=n()(e),s=o.data("value")+""==i+"";o.toggleClass("checked",s)}),e.find(".note-current-fontsizeunit").text(i)}if(o["line-height"]){const t=o["line-height"];e.find(".dropdown-line-height li a").each((e,o)=>{const i=n()(o).data("value")+""==t+"";this.className=i?"checked":""})}}updateBtnStates(t,e){n.a.each(e,(e,o)=>{this.ui.toggleBtnActive(t.find(e),o())})}tableMoveHandler(t){const e=n()(t.target.parentNode),o=e.next(),i=e.find(".note-dimension-picker-mousecatcher"),s=e.find(".note-dimension-picker-highlighted"),r=e.find(".note-dimension-picker-unhighlighted");let a;if(void 0===t.offsetX){const e=n()(t.target).offset();a={x:t.pageX-e.left,y:t.pageY-e.top}}else a={x:t.offsetX,y:t.offsetY};const l=Math.ceil(a.x/18)||1,c=Math.ceil(a.y/18)||1;s.css({width:l+"em",height:c+"em"}),i.data("value",l+"x"+c),l>3&&l<this.options.insertTableMaxSize.col&&r.css({width:l+1+"em"}),c>3&&c<this.options.insertTableMaxSize.row&&r.css({height:c+1+"em"}),o.html(l+" x "+c)}},toolbar:class{constructor(t){this.context=t,this.$window=n()(window),this.$document=n()(document),this.ui=n.a.summernote.ui,this.$note=t.layoutInfo.note,this.$editor=t.layoutInfo.editor,this.$toolbar=t.layoutInfo.toolbar,this.$editable=t.layoutInfo.editable,this.$statusbar=t.layoutInfo.statusbar,this.options=t.options,this.isFollowing=!1,this.followScroll=this.followScroll.bind(this)}shouldInitialize(){return!this.options.airMode}initialize(){this.options.toolbar=this.options.toolbar||[],this.options.toolbar.length?this.context.invoke("buttons.build",this.$toolbar,this.options.toolbar):this.$toolbar.hide(),this.options.toolbarContainer&&this.$toolbar.appendTo(this.options.toolbarContainer),this.changeContainer(!1),this.$note.on("summernote.keyup summernote.mouseup summernote.change",()=>{this.context.invoke("buttons.updateCurrentStyle")}),this.context.invoke("buttons.updateCurrentStyle"),this.options.followingToolbar&&this.$window.on("scroll resize",this.followScroll)}destroy(){this.$toolbar.children().remove(),this.options.followingToolbar&&this.$window.off("scroll resize",this.followScroll)}followScroll(){if(this.$editor.hasClass("fullscreen"))return!1;const t=this.$editor.outerHeight(),e=this.$editor.width(),o=this.$toolbar.height(),i=this.$statusbar.height();let s=0;this.options.otherStaticBar&&(s=n()(this.options.otherStaticBar).outerHeight());const r=this.$document.scrollTop(),a=this.$editor.offset().top,l=a-s,c=a+t-s-o-i;!this.isFollowing&&r>l&&r<c-o?(this.isFollowing=!0,this.$toolbar.css({position:"fixed",top:s,width:e,zIndex:1e3}),this.$editable.css({marginTop:this.$toolbar.height()+5})):this.isFollowing&&(r<l||r>c)&&(this.isFollowing=!1,this.$toolbar.css({position:"relative",top:0,width:"100%",zIndex:"auto"}),this.$editable.css({marginTop:""}))}changeContainer(t){t?this.$toolbar.prependTo(this.$editor):this.options.toolbarContainer&&this.$toolbar.appendTo(this.options.toolbarContainer),this.options.followingToolbar&&this.followScroll()}updateFullscreen(t){this.ui.toggleBtnActive(this.$toolbar.find(".btn-fullscreen"),t),this.changeContainer(t)}updateCodeview(t){this.ui.toggleBtnActive(this.$toolbar.find(".btn-codeview"),t),t?this.deactivate():this.activate()}activate(t){let e=this.$toolbar.find("button");t||(e=e.not(".btn-codeview").not(".btn-fullscreen")),this.ui.toggleBtn(e,!0)}deactivate(t){let e=this.$toolbar.find("button");t||(e=e.not(".btn-codeview").not(".btn-fullscreen")),this.ui.toggleBtn(e,!1)}},linkDialog:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.$body=n()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo,t.memo("help.linkDialog.show",this.options.langInfo.help["linkDialog.show"])}initialize(){const t=this.options.dialogsInBody?this.$body:this.options.container,e=['<div class="form-group note-form-group">',`<label for="note-dialog-link-txt-${this.options.id}" class="note-form-label">${this.lang.link.textToDisplay}</label>`,`<input id="note-dialog-link-txt-${this.options.id}" class="note-link-text form-control note-form-control note-input" type="text"/>`,"</div>",'<div class="form-group note-form-group">',`<label for="note-dialog-link-url-${this.options.id}" class="note-form-label">${this.lang.link.url}</label>`,`<input id="note-dialog-link-url-${this.options.id}" class="note-link-url form-control note-form-control note-input" type="text" value="http://"/>`,"</div>",this.options.disableLinkTarget?"":n()("<div/>").append(this.ui.checkbox({className:"sn-checkbox-open-in-new-window",text:this.lang.link.openInNewWindow,checked:!0}).render()).html(),n()("<div/>").append(this.ui.checkbox({className:"sn-checkbox-use-protocol",text:this.lang.link.useProtocol,checked:!0}).render()).html()].join(""),o=`<input type="button" href="#" class="btn btn-primary note-btn note-btn-primary note-link-btn" value="${this.lang.link.insert}" disabled>`;this.$dialog=this.ui.dialog({className:"link-dialog",title:this.lang.link.insert,fade:this.options.dialogsFade,body:e,footer:o}).render().appendTo(t)}destroy(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}bindEnterKey(t,e){t.on("keypress",t=>{t.keyCode===kt.code.ENTER&&(t.preventDefault(),e.trigger("click"))})}toggleLinkBtn(t,e,o){this.ui.toggleBtn(t,e.val()&&o.val())}showLinkDialog(t){return n.a.Deferred(e=>{const o=this.$dialog.find(".note-link-text"),i=this.$dialog.find(".note-link-url"),n=this.$dialog.find(".note-link-btn"),s=this.$dialog.find(".sn-checkbox-open-in-new-window input[type=checkbox]"),r=this.$dialog.find(".sn-checkbox-use-protocol input[type=checkbox]");this.ui.onDialogShown(this.$dialog,()=>{this.context.triggerEvent("dialog.shown"),!t.url&&b.isValidUrl(t.text)&&(t.url=t.text),o.on("input paste propertychange",()=>{t.text=o.val(),this.toggleLinkBtn(n,o,i)}).val(t.text),i.on("input paste propertychange",()=>{t.text||o.val(i.val()),this.toggleLinkBtn(n,o,i)}).val(t.url),f.isSupportTouch||i.trigger("focus"),this.toggleLinkBtn(n,o,i),this.bindEnterKey(i,n),this.bindEnterKey(o,n);const a=void 0!==t.isNewWindow?t.isNewWindow:this.context.options.linkTargetBlank;s.prop("checked",a);const l=!t.url&&this.context.options.useProtocol;r.prop("checked",l),n.one("click",n=>{n.preventDefault(),e.resolve({range:t.range,url:i.val(),text:o.val(),isNewWindow:s.is(":checked"),checkProtocol:r.is(":checked")}),this.ui.hideDialog(this.$dialog)})}),this.ui.onDialogHidden(this.$dialog,()=>{o.off(),i.off(),n.off(),"pending"===e.state()&&e.reject()}),this.ui.showDialog(this.$dialog)}).promise()}show(){const t=this.context.invoke("editor.getLinkInfo");this.context.invoke("editor.saveRange"),this.showLinkDialog(t).then(t=>{this.context.invoke("editor.restoreRange"),this.context.invoke("editor.createLink",t)}).fail(()=>{this.context.invoke("editor.restoreRange")})}},linkPopover:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.options=t.options,this.events={"summernote.keyup summernote.mouseup summernote.change summernote.scroll":()=>{this.update()},"summernote.disable summernote.dialog.shown summernote.blur":()=>{this.hide()}}}shouldInitialize(){return!y.isEmpty(this.options.popover.link)}initialize(){this.$popover=this.ui.popover({className:"note-link-popover",callback:t=>{t.find(".popover-content,.note-popover-content").prepend('<span><a target="_blank"></a>&nbsp;</span>')}}).render().appendTo(this.options.container);const t=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",t,this.options.popover.link),this.$popover.on("mousedown",t=>{t.preventDefault()})}destroy(){this.$popover.remove()}update(){if(!this.context.invoke("editor.hasFocus"))return void this.hide();const t=this.context.invoke("editor.getLastRange");if(t.isCollapsed()&&t.isOnAnchor()){const e=ut.ancestor(t.sc,ut.isAnchor),o=n()(e).attr("href");this.$popover.find("a").attr("href",o).html(o);const i=ut.posFromPlaceholder(e),s=n()(this.options.container).offset();i.top-=s.top,i.left-=s.left,this.$popover.css({display:"block",left:i.left,top:i.top})}else this.hide()}hide(){this.$popover.hide()}},imageDialog:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.$body=n()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}initialize(){let t="";if(this.options.maximumImageFileSize){const e=Math.floor(Math.log(this.options.maximumImageFileSize)/Math.log(1024)),o=1*(this.options.maximumImageFileSize/Math.pow(1024,e)).toFixed(2)+" "+" KMGTP"[e]+"B";t=`<small>${this.lang.image.maximumFileSize+" : "+o}</small>`}const e=this.options.dialogsInBody?this.$body:this.options.container,o=['<div class="form-group note-form-group note-group-select-from-files">','<label for="note-dialog-image-file-'+this.options.id+'" class="note-form-label">'+this.lang.image.selectFromFiles+"</label>",'<input id="note-dialog-image-file-'+this.options.id+'" class="note-image-input form-control-file note-form-control note-input" ',' type="file" name="files" accept="image/*" multiple="multiple"/>',t,"</div>",'<div class="form-group note-group-image-url">','<label for="note-dialog-image-url-'+this.options.id+'" class="note-form-label">'+this.lang.image.url+"</label>",'<input id="note-dialog-image-url-'+this.options.id+'" class="note-image-url form-control note-form-control note-input" type="text"/>',"</div>"].join(""),i=`<input type="button" href="#" class="btn btn-primary note-btn note-btn-primary note-image-btn" value="${this.lang.image.insert}" disabled>`;this.$dialog=this.ui.dialog({title:this.lang.image.insert,fade:this.options.dialogsFade,body:o,footer:i}).render().appendTo(e)}destroy(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}bindEnterKey(t,e){t.on("keypress",t=>{t.keyCode===kt.code.ENTER&&(t.preventDefault(),e.trigger("click"))})}show(){this.context.invoke("editor.saveRange"),this.showImageDialog().then(t=>{this.ui.hideDialog(this.$dialog),this.context.invoke("editor.restoreRange"),"string"==typeof t?this.options.callbacks.onImageLinkInsert?this.context.triggerEvent("image.link.insert",t):this.context.invoke("editor.insertImage",t):this.context.invoke("editor.insertImagesOrCallback",t)}).fail(()=>{this.context.invoke("editor.restoreRange")})}showImageDialog(){return n.a.Deferred(t=>{const e=this.$dialog.find(".note-image-input"),o=this.$dialog.find(".note-image-url"),i=this.$dialog.find(".note-image-btn");this.ui.onDialogShown(this.$dialog,()=>{this.context.triggerEvent("dialog.shown"),e.replaceWith(e.clone().on("change",e=>{t.resolve(e.target.files||e.target.value)}).val("")),o.on("input paste propertychange",()=>{this.ui.toggleBtn(i,o.val())}).val(""),f.isSupportTouch||o.trigger("focus"),i.click(e=>{e.preventDefault(),t.resolve(o.val())}),this.bindEnterKey(o,i)}),this.ui.onDialogHidden(this.$dialog,()=>{e.off(),o.off(),i.off(),"pending"===t.state()&&t.reject()}),this.ui.showDialog(this.$dialog)})}},imagePopover:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.editable=t.layoutInfo.editable[0],this.options=t.options,this.events={"summernote.disable summernote.blur":()=>{this.hide()}}}shouldInitialize(){return!y.isEmpty(this.options.popover.image)}initialize(){this.$popover=this.ui.popover({className:"note-image-popover"}).render().appendTo(this.options.container);const t=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",t,this.options.popover.image),this.$popover.on("mousedown",t=>{t.preventDefault()})}destroy(){this.$popover.remove()}update(t,e){if(ut.isImg(t)){const o=n()(t).offset(),i=n()(this.options.container).offset();let s={};this.options.popatmouse?(s.left=e.pageX-20,s.top=e.pageY):s=o,s.top-=i.top,s.left-=i.left,this.$popover.css({display:"block",left:s.left,top:s.top})}else this.hide()}hide(){this.$popover.hide()}},tablePopover:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.options=t.options,this.events={"summernote.mousedown":(t,e)=>{this.update(e.target)},"summernote.keyup summernote.scroll summernote.change":()=>{this.update()},"summernote.disable summernote.blur":()=>{this.hide()}}}shouldInitialize(){return!y.isEmpty(this.options.popover.table)}initialize(){this.$popover=this.ui.popover({className:"note-table-popover"}).render().appendTo(this.options.container);const t=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",t,this.options.popover.table),f.isFF&&document.execCommand("enableInlineTableEditing",!1,!1),this.$popover.on("mousedown",t=>{t.preventDefault()})}destroy(){this.$popover.remove()}update(t){if(this.context.isDisabled())return!1;const e=ut.isCell(t);if(e){const e=ut.posFromPlaceholder(t),o=n()(this.options.container).offset();e.top-=o.top,e.left-=o.left,this.$popover.css({display:"block",left:e.left,top:e.top})}else this.hide();return e}hide(){this.$popover.hide()}},videoDialog:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.$body=n()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}initialize(){const t=this.options.dialogsInBody?this.$body:this.options.container,e=['<div class="form-group note-form-group row-fluid">',`<label for="note-dialog-video-url-${this.options.id}" class="note-form-label">${this.lang.video.url} <small class="text-muted">${this.lang.video.providers}</small></label>`,`<input id="note-dialog-video-url-${this.options.id}" class="note-video-url form-control note-form-control note-input" type="text"/>`,"</div>"].join(""),o=`<input type="button" href="#" class="btn btn-primary note-btn note-btn-primary note-video-btn" value="${this.lang.video.insert}" disabled>`;this.$dialog=this.ui.dialog({title:this.lang.video.insert,fade:this.options.dialogsFade,body:e,footer:o}).render().appendTo(t)}destroy(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}bindEnterKey(t,e){t.on("keypress",t=>{t.keyCode===kt.code.ENTER&&(t.preventDefault(),e.trigger("click"))})}createVideoNode(t){const e=/^(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?$/,o=t.match(/\/\/(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?$/),i=t.match(/(?:www\.|\/\/)instagram\.com\/p\/(.[a-zA-Z0-9_-]*)/),s=t.match(/\/\/vine\.co\/v\/([a-zA-Z0-9]+)/),r=t.match(/\/\/(player\.)?vimeo\.com\/([a-z]*\/)*(\d+)[?]?.*/),a=t.match(/.+dailymotion.com\/(video|hub)\/([^_]+)[^#]*(#video=([^_&]+))?/),l=t.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/),c=t.match(/\/\/v\.qq\.com.*?vid=(.+)/),d=t.match(/\/\/v\.qq\.com\/x?\/?(page|cover).*?\/([^\/]+)\.html\??.*/),h=t.match(/^.+.(mp4|m4v)$/),u=t.match(/^.+.(ogg|ogv)$/),p=t.match(/^.+.(webm)$/),m=t.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/);let f;if(o&&11===o[1].length){const t=o[1];var g=0;if(void 0!==o[2]){const t=o[2].match(e);if(t)for(var b=[3600,60,1],v=0,k=b.length;v<k;v++)g+=void 0!==t[v+1]?b[v]*parseInt(t[v+1],10):0}f=n()("<iframe>").attr("frameborder",0).attr("src","//www.youtube.com/embed/"+t+(g>0?"?start="+g:"")).attr("width","640").attr("height","360")}else if(i&&i[0].length)f=n()("<iframe>").attr("frameborder",0).attr("src","https://instagram.com/p/"+i[1]+"/embed/").attr("width","612").attr("height","710").attr("scrolling","no").attr("allowtransparency","true");else if(s&&s[0].length)f=n()("<iframe>").attr("frameborder",0).attr("src",s[0]+"/embed/simple").attr("width","600").attr("height","600").attr("class","vine-embed");else if(r&&r[3].length)f=n()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("src","//player.vimeo.com/video/"+r[3]).attr("width","640").attr("height","360");else if(a&&a[2].length)f=n()("<iframe>").attr("frameborder",0).attr("src","//www.dailymotion.com/embed/video/"+a[2]).attr("width","640").attr("height","360");else if(l&&l[1].length)f=n()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","498").attr("width","510").attr("src","//player.youku.com/embed/"+l[1]);else if(c&&c[1].length||d&&d[2].length){const t=c&&c[1].length?c[1]:d[2];f=n()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","310").attr("width","500").attr("src","https://v.qq.com/iframe/player.html?vid="+t+"&amp;auto=0")}else if(h||u||p)f=n()("<video controls>").attr("src",t).attr("width","640").attr("height","360");else{if(!m||!m[0].length)return!1;f=n()("<iframe>").attr("frameborder",0).attr("src","https://www.facebook.com/plugins/video.php?href="+encodeURIComponent(m[0])+"&show_text=0&width=560").attr("width","560").attr("height","301").attr("scrolling","no").attr("allowtransparency","true")}return f.addClass("note-video-clip"),f[0]}show(){const t=this.context.invoke("editor.getSelectedText");this.context.invoke("editor.saveRange"),this.showVideoDialog(t).then(t=>{this.ui.hideDialog(this.$dialog),this.context.invoke("editor.restoreRange");const e=this.createVideoNode(t);e&&this.context.invoke("editor.insertNode",e)}).fail(()=>{this.context.invoke("editor.restoreRange")})}showVideoDialog(t){return n.a.Deferred(t=>{const e=this.$dialog.find(".note-video-url"),o=this.$dialog.find(".note-video-btn");this.ui.onDialogShown(this.$dialog,()=>{this.context.triggerEvent("dialog.shown"),e.on("input paste propertychange",()=>{this.ui.toggleBtn(o,e.val())}),f.isSupportTouch||e.trigger("focus"),o.click(o=>{o.preventDefault(),t.resolve(e.val())}),this.bindEnterKey(e,o)}),this.ui.onDialogHidden(this.$dialog,()=>{e.off(),o.off(),"pending"===t.state()&&t.reject()}),this.ui.showDialog(this.$dialog)})}},helpDialog:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.$body=n()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}initialize(){const t=this.options.dialogsInBody?this.$body:this.options.container,e=['<p class="text-center">','<a href="http://summernote.org/" target="_blank">Summernote 0.8.15</a> · ','<a href="https://github.com/summernote/summernote" target="_blank">Project</a> · ','<a href="https://github.com/summernote/summernote/issues" target="_blank">Issues</a>',"</p>"].join("");this.$dialog=this.ui.dialog({title:this.lang.options.help,fade:this.options.dialogsFade,body:this.createShortcutList(),footer:e,callback:t=>{t.find(".modal-body,.note-modal-body").css({"max-height":300,overflow:"scroll"})}}).render().appendTo(t)}destroy(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}createShortcutList(){const t=this.options.keyMap[f.isMac?"mac":"pc"];return Object.keys(t).map(e=>{const o=t[e],i=n()('<div><div class="help-list-item"/></div>');return i.append(n()("<label><kbd>"+e+"</kdb></label>").css({width:180,"margin-right":10})).append(n()("<span/>").html(this.context.memo("help."+o)||o)),i.html()}).join("")}showHelpDialog(){return n.a.Deferred(t=>{this.ui.onDialogShown(this.$dialog,()=>{this.context.triggerEvent("dialog.shown"),t.resolve()}),this.ui.showDialog(this.$dialog)}).promise()}show(){this.context.invoke("editor.saveRange"),this.showHelpDialog().then(()=>{this.context.invoke("editor.restoreRange")})}},airPopover:class{constructor(t){this.context=t,this.ui=n.a.summernote.ui,this.options=t.options,this.hidable=!0,this.events={"summernote.keyup summernote.mouseup summernote.scroll":()=>{this.options.editing&&this.update()},"summernote.disable summernote.change summernote.dialog.shown summernote.blur":()=>{this.hide()},"summernote.focusout":(t,e)=>{this.$popover.is(":active,:focus")||this.hide()}}}shouldInitialize(){return this.options.airMode&&!y.isEmpty(this.options.popover.air)}initialize(){this.$popover=this.ui.popover({className:"note-air-popover"}).render().appendTo(this.options.container);const t=this.$popover.find(".popover-content");this.context.invoke("buttons.build",t,this.options.popover.air),this.$popover.on("mousedown",()=>{this.hidable=!1}),this.$popover.on("mouseup",()=>{this.hidable=!0})}destroy(){this.$popover.remove()}update(){const t=this.context.invoke("editor.currentStyle");if(t.range&&!t.range.isCollapsed()){const e=y.last(t.range.getClientRects());if(e){const t=b.rect2bnd(e);this.$popover.css({display:"block",left:Math.max(t.left+t.width/2,0)-20,top:t.top+t.height}),this.context.invoke("buttons.updateCurrentStyle",this.$popover)}}else this.hide()}hide(){this.hidable&&this.$popover.hide()}}},buttons:{},lang:"en-US",followingToolbar:!1,toolbarPosition:"top",otherStaticBar:"",toolbar:[["style",["style"]],["font",["bold","underline","clear"]],["fontname",["fontname"]],["color",["color"]],["para",["ul","ol","paragraph"]],["table",["table"]],["insert",["link","picture","video"]],["view",["fullscreen","codeview","help"]]],popatmouse:!0,popover:{image:[["resize",["resizeFull","resizeHalf","resizeQuarter","resizeNone"]],["float",["floatLeft","floatRight","floatNone"]],["remove",["removeMedia"]]],link:[["link",["linkDialogShow","unlink"]]],table:[["add",["addRowDown","addRowUp","addColLeft","addColRight"]],["delete",["deleteRow","deleteCol","deleteTable"]]],air:[["color",["color"]],["font",["bold","underline","clear"]],["para",["ul","paragraph"]],["table",["table"]],["insert",["link","picture"]],["view",["fullscreen","codeview"]]]},airMode:!1,width:null,height:null,linkTargetBlank:!0,useProtocol:!0,defaultProtocol:"http://",focus:!1,tabDisabled:!1,tabSize:4,styleWithSpan:!0,shortcuts:!0,textareaAutoSync:!0,tooltip:"auto",container:null,maxTextLength:0,blockquoteBreakingLevel:2,spellCheck:!0,disableGrammar:!1,placeholder:null,inheritPlaceholder:!1,hintMode:"word",hintSelect:"after",hintDirection:"bottom",styleTags:["p","blockquote","pre","h1","h2","h3","h4","h5","h6"],fontNames:["Arial","Arial Black","Comic Sans MS","Courier New","Helvetica Neue","Helvetica","Impact","Lucida Grande","Tahoma","Times New Roman","Verdana"],fontNamesIgnoreCheck:[],addDefaultFonts:!0,fontSizes:["8","9","10","11","12","14","18","24","36"],fontSizeUnits:["px","pt"],colors:[["#000000","#424242","#636363","#9C9C94","#CEC6CE","#EFEFEF","#F7F7F7","#FFFFFF"],["#FF0000","#FF9C00","#FFFF00","#00FF00","#00FFFF","#0000FF","#9C00FF","#FF00FF"],["#F7C6CE","#FFE7CE","#FFEFC6","#D6EFD6","#CEDEE7","#CEE7F7","#D6D6E7","#E7D6DE"],["#E79C9C","#FFC69C","#FFE79C","#B5D6A5","#A5C6CE","#9CC6EF","#B5A5D6","#D6A5BD"],["#E76363","#F7AD6B","#FFD663","#94BD7B","#73A5AD","#6BADDE","#8C7BC6","#C67BA5"],["#CE0000","#E79439","#EFC631","#6BA54A","#4A7B8C","#3984C6","#634AA5","#A54A7B"],["#9C0000","#B56308","#BD9400","#397B21","#104A5A","#085294","#311873","#731842"],["#630000","#7B3900","#846300","#295218","#083139","#003163","#21104A","#4A1031"]],colorsName:[["Black","Tundora","Dove Gray","Star Dust","Pale Slate","Gallery","Alabaster","White"],["Red","Orange Peel","Yellow","Green","Cyan","Blue","Electric Violet","Magenta"],["Azalea","Karry","Egg White","Zanah","Botticelli","Tropical Blue","Mischka","Twilight"],["Tonys Pink","Peach Orange","Cream Brulee","Sprout","Casper","Perano","Cold Purple","Careys Pink"],["Mandy","Rajah","Dandelion","Olivine","Gulf Stream","Viking","Blue Marguerite","Puce"],["Guardsman Red","Fire Bush","Golden Dream","Chelsea Cucumber","Smalt Blue","Boston Blue","Butterfly Bush","Cadillac"],["Sangria","Mai Tai","Buddha Gold","Forest Green","Eden","Venice Blue","Meteorite","Claret"],["Rosewood","Cinnamon","Olive","Parsley","Tiber","Midnight Blue","Valentino","Loulou"]],colorButton:{foreColor:"#000000",backColor:"#FFFF00"},lineHeights:["1.0","1.2","1.4","1.5","1.6","1.8","2.0","3.0"],tableClassName:"table table-bordered",insertTableMaxSize:{col:10,row:10},dialogsInBody:!1,dialogsFade:!1,maximumImageFileSize:null,callbacks:{onBeforeCommand:null,onBlur:null,onBlurCodeview:null,onChange:null,onChangeCodeview:null,onDialogShown:null,onEnter:null,onFocus:null,onImageLinkInsert:null,onImageUpload:null,onImageUploadError:null,onInit:null,onKeydown:null,onKeyup:null,onMousedown:null,onMouseup:null,onPaste:null,onScroll:null},codemirror:{mode:"text/html",htmlMode:!0,lineNumbers:!0},codeviewFilter:!1,codeviewFilterRegex:/<\/*(?:applet|b(?:ase|gsound|link)|embed|frame(?:set)?|ilayer|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|t(?:itle|extarea)|xml)[^>]*?>/gi,codeviewIframeFilter:!0,codeviewIframeWhitelistSrc:[],codeviewIframeWhitelistSrcBase:["www.youtube.com","www.youtube-nocookie.com","www.facebook.com","vine.co","instagram.com","player.vimeo.com","www.dailymotion.com","player.youku.com","v.qq.com"],keyMap:{pc:{ENTER:"insertParagraph","CTRL+Z":"undo","CTRL+Y":"redo",TAB:"tab","SHIFT+TAB":"untab","CTRL+B":"bold","CTRL+I":"italic","CTRL+U":"underline","CTRL+SHIFT+S":"strikethrough","CTRL+BACKSLASH":"removeFormat","CTRL+SHIFT+L":"justifyLeft","CTRL+SHIFT+E":"justifyCenter","CTRL+SHIFT+R":"justifyRight","CTRL+SHIFT+J":"justifyFull","CTRL+SHIFT+NUM7":"insertUnorderedList","CTRL+SHIFT+NUM8":"insertOrderedList","CTRL+LEFTBRACKET":"outdent","CTRL+RIGHTBRACKET":"indent","CTRL+NUM0":"formatPara","CTRL+NUM1":"formatH1","CTRL+NUM2":"formatH2","CTRL+NUM3":"formatH3","CTRL+NUM4":"formatH4","CTRL+NUM5":"formatH5","CTRL+NUM6":"formatH6","CTRL+ENTER":"insertHorizontalRule","CTRL+K":"linkDialog.show"},mac:{ENTER:"insertParagraph","CMD+Z":"undo","CMD+SHIFT+Z":"redo",TAB:"tab","SHIFT+TAB":"untab","CMD+B":"bold","CMD+I":"italic","CMD+U":"underline","CMD+SHIFT+S":"strikethrough","CMD+BACKSLASH":"removeFormat","CMD+SHIFT+L":"justifyLeft","CMD+SHIFT+E":"justifyCenter","CMD+SHIFT+R":"justifyRight","CMD+SHIFT+J":"justifyFull","CMD+SHIFT+NUM7":"insertUnorderedList","CMD+SHIFT+NUM8":"insertOrderedList","CMD+LEFTBRACKET":"outdent","CMD+RIGHTBRACKET":"indent","CMD+NUM0":"formatPara","CMD+NUM1":"formatH1","CMD+NUM2":"formatH2","CMD+NUM3":"formatH3","CMD+NUM4":"formatH4","CMD+NUM5":"formatH5","CMD+NUM6":"formatH6","CMD+ENTER":"insertHorizontalRule","CMD+K":"linkDialog.show"}},icons:{align:"note-icon-align",alignCenter:"note-icon-align-center",alignJustify:"note-icon-align-justify",alignLeft:"note-icon-align-left",alignRight:"note-icon-align-right",rowBelow:"note-icon-row-below",colBefore:"note-icon-col-before",colAfter:"note-icon-col-after",rowAbove:"note-icon-row-above",rowRemove:"note-icon-row-remove",colRemove:"note-icon-col-remove",indent:"note-icon-align-indent",outdent:"note-icon-align-outdent",arrowsAlt:"note-icon-arrows-alt",bold:"note-icon-bold",caret:"note-icon-caret",circle:"note-icon-circle",close:"note-icon-close",code:"note-icon-code",eraser:"note-icon-eraser",floatLeft:"note-icon-float-left",floatRight:"note-icon-float-right",font:"note-icon-font",frame:"note-icon-frame",italic:"note-icon-italic",link:"note-icon-link",unlink:"note-icon-chain-broken",magic:"note-icon-magic",menuCheck:"note-icon-menu-check",minus:"note-icon-minus",orderedlist:"note-icon-orderedlist",pencil:"note-icon-pencil",picture:"note-icon-picture",question:"note-icon-question",redo:"note-icon-redo",rollback:"note-icon-rollback",square:"note-icon-square",strikethrough:"note-icon-strikethrough",subscript:"note-icon-subscript",superscript:"note-icon-superscript",table:"note-icon-table",textHeight:"note-icon-text-height",trash:"note-icon-trash",underline:"note-icon-underline",undo:"note-icon-undo",unorderedlist:"note-icon-unorderedlist",video:"note-icon-video"}}})},50:function(t,e,o){"use strict";o.r(e);var i=o(0),n=o.n(i),s=o(1);var r=class{constructor(t,e){if(this.$node=t,this.options=n.a.extend({},{title:"",target:e.container,trigger:"hover focus",placement:"bottom"},e),this.$tooltip=n()(['<div class="note-tooltip">','<div class="note-tooltip-arrow"/>','<div class="note-tooltip-content"/>',"</div>"].join("")),"manual"!==this.options.trigger){const e=this.show.bind(this),o=this.hide.bind(this),i=this.toggle.bind(this);this.options.trigger.split(" ").forEach((function(n){"hover"===n?(t.off("mouseenter mouseleave"),t.on("mouseenter",e).on("mouseleave",o)):"click"===n?t.on("click",i):"focus"===n&&t.on("focus",e).on("blur",o)}))}}show(){const t=this.$node,e=t.offset(),o=n()(this.options.target).offset();e.top-=o.top,e.left-=o.left;const i=this.$tooltip,s=this.options.title||t.attr("title")||t.data("title"),r=this.options.placement||t.data("placement");i.addClass(r),i.find(".note-tooltip-content").text(s),i.appendTo(this.options.target);const a=t.outerWidth(),l=t.outerHeight(),c=i.outerWidth(),d=i.outerHeight();"bottom"===r?i.css({top:e.top+l,left:e.left+(a/2-c/2)}):"top"===r?i.css({top:e.top-d,left:e.left+(a/2-c/2)}):"left"===r?i.css({top:e.top+(l/2-d/2),left:e.left-c}):"right"===r&&i.css({top:e.top+(l/2-d/2),left:e.left+a}),i.addClass("in")}hide(){this.$tooltip.removeClass("in"),setTimeout(()=>{this.$tooltip.remove()},200)}toggle(){this.$tooltip.hasClass("in")?this.hide():this.show()}};n()(document).on("click",(function(t){n()(t.target).closest(".note-btn-group").length||(n()(".note-btn-group.open").removeClass("open"),n()(".note-btn-group .note-btn.active").removeClass("active"))})),n()(document).on("click.note-dropdown-menu",(function(t){n()(t.target).closest(".note-dropdown-menu").parent().removeClass("open"),n()(t.target).closest(".note-dropdown-menu").parent().find(".note-btn.active").removeClass("active")}));var a=class{constructor(t,e){this.$button=t,this.options=n.a.extend({},{target:e.container},e),this.setEvent()}setEvent(){this.$button.on("click",t=>{this.toggle(),t.stopImmediatePropagation()})}clear(){var t=n()(".note-btn-group.open");t.find(".note-btn.active").removeClass("active"),t.removeClass("open")}show(){this.$button.addClass("active"),this.$button.parent().addClass("open");var t=this.$button.next(),e=t.offset(),o=t.outerWidth(),i=n()(window).width(),s=parseFloat(n()(this.options.target).css("margin-right"));e.left+o>i-s?t.css("margin-left",i-s-(e.left+o)):t.css("margin-left","")}hide(){this.$button.removeClass("active"),this.$button.parent().removeClass("open")}toggle(){var t=this.$button.parent().hasClass("open");this.clear(),t?this.hide():this.show()}};var l=class{constructor(t,e){this.$modal=t,this.$backdrop=n()('<div class="note-modal-backdrop"/>')}show(){this.$backdrop.appendTo(document.body).show(),this.$modal.addClass("open").show(),this.$modal.trigger("note.modal.show"),this.$modal.off("click",".close").on("click",".close",this.hide.bind(this)),this.$modal.on("keydown",t=>{27===t.which&&(t.preventDefault(),this.hide())})}hide(){this.$modal.removeClass("open").hide(),this.$backdrop.hide(),this.$modal.trigger("note.modal.hide"),this.$modal.off("keydown")}};const c=s.a.create('<div class="note-editor note-frame"/>'),d=s.a.create('<div class="note-toolbar" role="toolbar"/>'),h=s.a.create('<div class="note-editing-area"/>'),u=s.a.create('<textarea class="note-codable" aria-multiline="true"/>'),p=s.a.create('<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"/>'),m=s.a.create(['<output class="note-status-output" role="status" aria-live="polite"/>','<div class="note-statusbar" role="status">','<div class="note-resizebar" aria-label="resize">','<div class="note-icon-bar"/>','<div class="note-icon-bar"/>','<div class="note-icon-bar"/>',"</div>","</div>"].join("")),f=s.a.create('<div class="note-editor note-airframe"/>'),g=s.a.create(['<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"/>','<output class="note-status-output" role="status" aria-live="polite"/>'].join("")),b=s.a.create('<div class="note-btn-group">'),v=s.a.create('<button type="button" class="note-btn" tabindex="-1">',(function(t,e){e&&e.tooltip&&(t.attr({"aria-label":e.tooltip}),t.data("_lite_tooltip",new r(t,{title:e.tooltip,container:e.container})).on("click",t=>{n()(t.currentTarget).data("_lite_tooltip").hide()})),e.contents&&t.html(e.contents),e&&e.data&&"dropdown"===e.data.toggle&&t.data("_lite_dropdown",new a(t,{container:e.container}))})),k=s.a.create('<div class="note-dropdown-menu" role="list">',(function(t,e){const o=Array.isArray(e.items)?e.items.map((function(t){const o="string"==typeof t?t:t.value||"",i=e.template?e.template(t):t,s=n()('<a class="note-dropdown-item" href="#" data-value="'+o+'" role="listitem" aria-label="'+o+'"></a>');return s.html(i).data("item",t),s})):e.items;t.html(o).attr({"aria-label":e.title}),t.on("click","> .note-dropdown-item",(function(t){const o=n()(this),i=o.data("item"),s=o.data("value");i.click?i.click(o):e.itemClick&&e.itemClick(t,i,s)}))})),C=s.a.create('<div class="note-dropdown-menu note-check" role="list">',(function(t,e){const o=Array.isArray(e.items)?e.items.map((function(t){const o="string"==typeof t?t:t.value||"",i=e.template?e.template(t):t,s=n()('<a class="note-dropdown-item" href="#" data-value="'+o+'" role="listitem" aria-label="'+t+'"></a>');return s.html([F(e.checkClassName)," ",i]).data("item",t),s})):e.items;t.html(o).attr({"aria-label":e.title}),t.on("click","> .note-dropdown-item",(function(t){const o=n()(this),i=o.data("item"),s=o.data("value");i.click?i.click(o):e.itemClick&&e.itemClick(t,i,s)}))})),w=function(t,e){return t+" "+F(e.icons.caret,"span")},y=function(t,e){return b([v({className:"dropdown-toggle",contents:t.title+" "+F("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),k({className:t.className,items:t.items,template:t.template,itemClick:t.itemClick})],{callback:e}).render()},x=function(t,e){return b([v({className:"dropdown-toggle",contents:t.title+" "+F("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),C({className:t.className,checkClassName:t.checkClassName,items:t.items,template:t.template,itemClick:t.itemClick})],{callback:e}).render()},S=function(t){return b([v({className:"dropdown-toggle",contents:t.title+" "+F("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),k([b({className:"note-align",children:t.items[0]}),b({className:"note-list",children:t.items[1]})])]).render()},$=function(t){return b([v({className:"dropdown-toggle",contents:t.title+" "+F("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),k({className:"note-table",items:['<div class="note-dimension-picker">','<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"/>','<div class="note-dimension-picker-highlighted"/>','<div class="note-dimension-picker-unhighlighted"/>',"</div>",'<div class="note-dimension-display">1 x 1</div>'].join("")})],{callback:function(e){e.find(".note-dimension-picker-mousecatcher").css({width:t.col+"em",height:t.row+"em"}).mousedown(t.itemClick).mousemove((function(e){!function(t,e,o){const i=n()(t.target.parentNode),s=i.next(),r=i.find(".note-dimension-picker-mousecatcher"),a=i.find(".note-dimension-picker-highlighted"),l=i.find(".note-dimension-picker-unhighlighted");let c;if(void 0===t.offsetX){const e=n()(t.target).offset();c={x:t.pageX-e.left,y:t.pageY-e.top}}else c={x:t.offsetX,y:t.offsetY};const d=Math.ceil(c.x/18)||1,h=Math.ceil(c.y/18)||1;a.css({width:d+"em",height:h+"em"}),r.data("value",d+"x"+h),d>3&&d<e&&l.css({width:d+1+"em"}),h>3&&h<o&&l.css({height:h+1+"em"}),s.html(d+" x "+h)}(e,t.col,t.row)}))}}).render()},I=s.a.create('<div class="note-color-palette"/>',(function(t,e){const o=[];for(let t=0,i=e.colors.length;t<i;t++){const i=e.eventName,n=e.colors[t],s=e.colorsName[t],r=[];for(let t=0,e=n.length;t<e;t++){const e=n[t],o=s[t];r.push(['<button type="button" class="note-btn note-color-btn"','style="background-color:',e,'" ','data-event="',i,'" ','data-value="',e,'" ','data-title="',o,'" ','aria-label="',o,'" ','data-toggle="button" tabindex="-1"></button>'].join(""))}o.push('<div class="note-color-row">'+r.join("")+"</div>")}t.html(o.join("")),t.find(".note-color-btn").each((function(){n()(this).data("_lite_tooltip",new r(n()(this),{container:e.container}))}))})),N=function(t,e){return b({className:"note-color",children:[v({className:"note-current-color-button",contents:t.title,tooltip:t.lang.color.recent,click:t.currentClick,callback:function(t){const o=t.find(".note-recent-color");"foreColor"!==e&&(o.css("background-color","#FFFF00"),t.attr("data-backColor","#FFFF00"))}}),v({className:"dropdown-toggle",contents:F("note-icon-caret"),tooltip:t.lang.color.more,data:{toggle:"dropdown"}}),k({items:["<div>",'<div class="note-btn-group btn-background-color">','<div class="note-palette-title">'+t.lang.color.background+"</div>","<div>",'<button type="button" class="note-color-reset note-btn note-btn-block" data-event="backColor" data-value="inherit">',t.lang.color.transparent,"</button>","</div>",'<div class="note-holder" data-event="backColor"/>','<div class="btn-sm">','<input type="color" id="html5bcp" class="note-btn btn-default" value="#21104A" style="width:100%;" data-value="cp">','<button type="button" class="note-color-reset btn" data-event="backColor" data-value="cpbackColor">',t.lang.color.cpSelect,"</button>","</div>","</div>",'<div class="note-btn-group btn-foreground-color">','<div class="note-palette-title">'+t.lang.color.foreground+"</div>","<div>",'<button type="button" class="note-color-reset note-btn note-btn-block" data-event="removeFormat" data-value="foreColor">',t.lang.color.resetToDefault,"</button>","</div>",'<div class="note-holder" data-event="foreColor"/>','<div class="btn-sm">','<input type="color" id="html5fcp" class="note-btn btn-default" value="#21104A" style="width:100%;" data-value="cp">','<button type="button" class="note-color-reset btn" data-event="foreColor" data-value="cpforeColor">',t.lang.color.cpSelect,"</button>","</div>","</div>","</div>"].join(""),callback:function(o){o.find(".note-holder").each((function(){const e=n()(this);e.append(I({colors:t.colors,eventName:e.data("event")}).render())})),"fore"===e?(o.find(".btn-background-color").hide(),o.css({"min-width":"210px"})):"back"===e&&(o.find(".btn-foreground-color").hide(),o.css({"min-width":"210px"}))},click:function(o){const i=n()(o.target),s=i.data("event");let r=i.data("value");const a=document.getElementById("html5fcp").value,l=document.getElementById("html5bcp").value;if("cp"===r?o.stopPropagation():"cpbackColor"===r?r=l:"cpforeColor"===r&&(r=a),s&&r){const o="backColor"===s?"background-color":"color",n=i.closest(".note-color").find(".note-recent-color"),a=i.closest(".note-color").find(".note-current-color-button");n.css(o,r),a.attr("data-"+s,r),"fore"===e?t.itemClick("foreColor",r):"back"===e?t.itemClick("backColor",r):t.itemClick(s,r)}}})]}).render()},T=s.a.create('<div class="note-modal" aria-hidden="false" tabindex="-1" role="dialog"/>',(function(t,e){e.fade&&t.addClass("fade"),t.attr({"aria-label":e.title}),t.html(['<div class="note-modal-content">',e.title?'<div class="note-modal-header"><button type="button" class="close" aria-label="Close" aria-hidden="true"><i class="note-icon-close"></i></button><h4 class="note-modal-title">'+e.title+"</h4></div>":"",'<div class="note-modal-body">'+e.body+"</div>",e.footer?'<div class="note-modal-footer">'+e.footer+"</div>":"","</div>"].join("")),t.data("modal",new l(t,e))})),E=function(t){const e='<div class="note-form-group"><label for="note-dialog-video-url-'+t.id+'" class="note-form-label">'+t.lang.video.url+' <small class="text-muted">'+t.lang.video.providers+'</small></label><input id="note-dialog-video-url-'+t.id+'" class="note-video-url note-input" type="text"/></div>',o=['<button type="button" href="#" class="note-btn note-btn-primary note-video-btn disabled" disabled>',t.lang.video.insert,"</button>"].join("");return T({title:t.lang.video.insert,fade:t.fade,body:e,footer:o}).render()},R=function(t){const e='<div class="note-form-group note-group-select-from-files"><label for="note-dialog-image-file-'+t.id+'" class="note-form-label">'+t.lang.image.selectFromFiles+'</label><input id="note-dialog-image-file-'+t.id+'" class="note-note-image-input note-input" type="file" name="files" accept="image/*" multiple="multiple"/>'+t.imageLimitation+'</div><div class="note-form-group"><label for="note-dialog-image-url-'+t.id+'" class="note-form-label">'+t.lang.image.url+'</label><input id="note-dialog-image-url-'+t.id+'" class="note-image-url note-input" type="text"/></div>',o=['<button href="#" type="button" class="note-btn note-btn-primary note-btn-large note-image-btn disabled" disabled>',t.lang.image.insert,"</button>"].join("");return T({title:t.lang.image.insert,fade:t.fade,body:e,footer:o}).render()},L=function(t){const e='<div class="note-form-group"><label for="note-dialog-link-txt-'+t.id+'" class="note-form-label">'+t.lang.link.textToDisplay+'</label><input id="note-dialog-link-txt-'+t.id+'" class="note-link-text note-input" type="text"/></div><div class="note-form-group"><label for="note-dialog-link-url-'+t.id+'" class="note-form-label">'+t.lang.link.url+'</label><input id="note-dialog-link-url-'+t.id+'" class="note-link-url note-input" type="text" value="http://"/></div>'+(t.disableLinkTarget?"":'<div class="checkbox"><label for="note-dialog-link-nw-'+t.id+'"><input id="note-dialog-link-nw-'+t.id+'" type="checkbox" checked> '+t.lang.link.openInNewWindow+"</label></div>")+'<div class="checkbox"><label for="note-dialog-link-up-'+t.id+'"><input id="note-dialog-link-up-'+t.id+'" type="checkbox" checked> '+t.lang.link.useProtocol+"</label></div>",o=['<button href="#" type="button" class="note-btn note-btn-primary note-link-btn disabled" disabled>',t.lang.link.insert,"</button>"].join("");return T({className:"link-dialog",title:t.lang.link.insert,fade:t.fade,body:e,footer:o}).render()},A=s.a.create(['<div class="note-popover bottom">','<div class="note-popover-arrow"/>','<div class="popover-content note-children-container"/>',"</div>"].join(""),(function(t,e){const o=void 0!==e.direction?e.direction:"bottom";t.addClass(o).hide(),e.hideArrow&&t.find(".note-popover-arrow").hide()})),P=s.a.create('<div class="checkbox"></div>',(function(t,e){t.html(["<label"+(e.id?' for="note-'+e.id+'"':"")+">",'<input role="checkbox" type="checkbox"'+(e.id?' id="note-'+e.id+'"':""),e.checked?" checked":"",' aria-checked="'+(e.checked?"true":"false")+'"/>',e.text?e.text:"","</label>"].join(""))})),F=function(t,e){return"<"+(e=e||"i")+' class="'+t+'"/>'};var D=function(t){return{editor:c,toolbar:d,editingArea:h,codable:u,editable:p,statusbar:m,airEditor:f,airEditable:g,buttonGroup:b,button:v,dropdown:k,dropdownCheck:C,dropdownButton:y,dropdownButtonContents:w,dropdownCheckButton:x,paragraphDropdownButton:S,tableDropdownButton:$,colorDropdownButton:N,palette:I,dialog:T,videoDialog:E,imageDialog:R,linkDialog:L,popover:A,checkbox:P,icon:F,options:t,toggleBtn:function(t,e){t.toggleClass("disabled",!e),t.attr("disabled",!e)},toggleBtnActive:function(t,e){t.toggleClass("active",e)},check:function(t,e){t.find(".checked").removeClass("checked"),t.find('[data-value="'+e+'"]').addClass("checked")},onDialogShown:function(t,e){t.one("note.modal.show",e)},onDialogHidden:function(t,e){t.one("note.modal.hide",e)},showDialog:function(t){t.data("modal").show()},hideDialog:function(t){t.data("modal").hide()},getPopoverContent:function(t){return t.find(".note-popover-content")},getDialogBody:function(t){return t.find(".note-modal-body")},createLayout:function(e){const o=(t.airMode?f([h([u(),g()])]):"bottom"===t.toolbarPosition?c([h([u(),p()]),d(),m()]):c([d(),h([u(),p()]),m()])).render();return o.insertAfter(e),{note:e,editor:o,toolbar:o.find(".note-toolbar"),editingArea:o.find(".note-editing-area"),editable:o.find(".note-editable"),codable:o.find(".note-codable"),statusbar:o.find(".note-statusbar")}},removeLayout:function(t,e){t.html(e.editable.html()),e.editor.remove(),t.off("summernote"),t.show()}}};o(3),o(6);n.a.summernote=n.a.extend(n.a.summernote,{ui_template:D,interface:"lite"})},6:function(t,e,o){}})}));
//# sourceMappingURL=summernote-lite.min.js.map