<?php

use Illuminate\Database\Seeder;
use App\Plan;
use App\User;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first user as creator
        $user = User::first();
        $userId = $user ? $user->id : null;

        Plan::create([
            'name' => 'Basic Plan',
            'duration' => '1 month',
            'no_of_allowed_fax' => 100,
            'status' => 'active',
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);

        Plan::create([
            'name' => 'Standard Plan',
            'duration' => '3 months',
            'no_of_allowed_fax' => 500,
            'status' => 'active',
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);

        Plan::create([
            'name' => 'Premium Plan',
            'duration' => '6 months',
            'no_of_allowed_fax' => 1000,
            'status' => 'active',
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);

        Plan::create([
            'name' => 'Enterprise Plan',
            'duration' => '1 year',
            'no_of_allowed_fax' => 5000,
            'status' => 'active',
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);

        Plan::create([
            'name' => 'Lifetime Plan',
            'duration' => 'lifetime',
            'no_of_allowed_fax' => 999999,
            'status' => 'inactive',
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);
    }
}
