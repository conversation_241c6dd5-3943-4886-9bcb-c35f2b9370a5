<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Country;
use App\State;
use App\Plan;
use App\Subscriber;
use App\User;
use Illuminate\Support\Facades\Auth;

class ApiController extends Controller
{
    public function getCountries()
    {
        try {
            $countries = Country::active()->orderBy('name')->get(['country_id', 'name']);
            if($countries->count() > 0){
                return response()->json($countries);
            }else{
                return response()->json(['message' => 'No countries found'], 404);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'Something went wrong'], 500);
        }

    }

    public function getStates()
    {
        try {
            $states = State::active()->orderBy('name')->get(['state_id', 'name']);
            if($states->count() > 0){
                return response()->json($states);
            }else{
                return response()->json(['message' => 'No states found'], 404);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'Something went wrong'], 500);
        }
    }

    public function getStatesByCountry($countryId)
    {
        try {
            $states = State::where('country_id', $countryId)
                          ->where('is_active', true)
                          ->orderBy('name')
                          ->get(['state_id', 'name']);
            if($states->count() > 0){
                return response()->json($states);
            }else{
                return response()->json(['message' => 'No states found'], 404);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'Something went wrong'], 500);
        }
    }

    public function getPlans()
    {
        try {
            $plans = Plan::active()->orderBy('name')->get(['id', 'name', 'duration', 'no_of_allowed_fax', 'status']);
            if($plans->count() > 0){
                return response()->json($plans);
            }else{
                return response()->json(['message' => 'No plans found'], 404);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'Something went wrong'], 500);
        }
    }

    public function getSubscribers()
    {
        try {
            $subscribers = Subscriber::with(['plan', 'country', 'state'])
                                    ->latest()
                                    ->get();
            if($subscribers->count() > 0){
                return response()->json($subscribers);
            }else{
                return response()->json(['message' => 'No subscribers found'], 404);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'Something went wrong'], 500);
        }
    }

    public function getSubscriber($id)
    {
        try {
            $subscriber = Subscriber::with(['plan', 'country', 'state'])
                                    ->findOrFail($id);
            return response()->json($subscriber);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Something went wrong'], 500);
        }
    }
}
