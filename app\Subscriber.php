<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Subscriber extends Model
{
    protected $fillable = [
        'subscriber_name',
        'email_id',
        'phone_no',
        'start_date',
        'expiry_date',
        'plan_id',
        'country_id',
        'state_id',
        'amount_paid',
        'status',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'start_date' => 'date',
        'expiry_date' => 'date',
        'amount_paid' => 'decimal:2',
    ];

    // Relationships
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class, 'plan_id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'country_id');
    }

    public function state()
    {
        return $this->belongsTo(State::class, 'state_id', 'state_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    public function scopeSuspended($query)
    {
        return $query->where('status', 'suspended');
    }

    public function scopeExpiring($query, $days = 30)
    {
        return $query->where('expiry_date', '<=', Carbon::now()->addDays($days))
                    ->where('status', 'active');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => '<span class="badge badge-success">Active</span>',
            'inactive' => '<span class="badge badge-secondary">Inactive</span>',
            'expired' => '<span class="badge badge-danger">Expired</span>',
            'suspended' => '<span class="badge badge-warning">Suspended</span>',
        ];

        return $badges[$this->status] ?? '<span class="badge badge-secondary">Unknown</span>';
    }

    public function getIsExpiredAttribute()
    {
        return $this->expiry_date < Carbon::now();
    }

    public function getDaysRemainingAttribute()
    {
        if ($this->is_expired) {
            return 0;
        }

        return Carbon::now()->diffInDays($this->expiry_date, false);
    }

    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount_paid, 2);
    }

    // Mutators
    public function setEmailIdAttribute($value)
    {
        $this->attributes['email_id'] = strtolower($value);
    }
}
