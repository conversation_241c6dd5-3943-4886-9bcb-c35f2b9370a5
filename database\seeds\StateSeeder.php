<?php

use Illuminate\Database\Seeder;
use App\State;
use App\Country;
use App\User;

class StateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first user as creator
        $user = User::first();
        $userId = $user ? $user->id : null;

        // Get countries
        $india = Country::where('code', 'IN')->first();
        $usa = Country::where('code', 'US')->first();
        $uk = Country::where('code', 'GB')->first();

        $states = [];

        // Indian States
        if ($india) {
            $states = array_merge($states, [
                [
                    'name' => 'Tamil Nadu',
                    'country_id' => $india->country_id,
                    'region_id' => 'TN',
                    'is_active' => true,
                    'is_language' => true,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
                [
                    'name' => 'Karnataka',
                    'country_id' => $india->country_id,
                    'region_id' => 'KA',
                    'is_active' => true,
                    'is_language' => true,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
                [
                    'name' => 'Maharashtra',
                    'country_id' => $india->country_id,
                    'region_id' => 'MH',
                    'is_active' => true,
                    'is_language' => true,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
                [
                    'name' => 'Kerala',
                    'country_id' => $india->country_id,
                    'region_id' => 'KL',
                    'is_active' => true,
                    'is_language' => true,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
                [
                    'name' => 'Delhi',
                    'country_id' => $india->country_id,
                    'region_id' => 'DL',
                    'is_active' => true,
                    'is_language' => false,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
            ]);
        }

        // US States
        if ($usa) {
            $states = array_merge($states, [
                [
                    'name' => 'California',
                    'country_id' => $usa->country_id,
                    'region_id' => 'CA',
                    'is_active' => true,
                    'is_language' => false,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
                [
                    'name' => 'New York',
                    'country_id' => $usa->country_id,
                    'region_id' => 'NY',
                    'is_active' => true,
                    'is_language' => false,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
                [
                    'name' => 'Texas',
                    'country_id' => $usa->country_id,
                    'region_id' => 'TX',
                    'is_active' => true,
                    'is_language' => false,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
            ]);
        }

        // UK States/Regions
        if ($uk) {
            $states = array_merge($states, [
                [
                    'name' => 'England',
                    'country_id' => $uk->country_id,
                    'region_id' => 'ENG',
                    'is_active' => true,
                    'is_language' => false,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
                [
                    'name' => 'Scotland',
                    'country_id' => $uk->country_id,
                    'region_id' => 'SCT',
                    'is_active' => true,
                    'is_language' => false,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ],
            ]);
        }

        foreach ($states as $state) {
            State::create($state);
        }
    }
}
