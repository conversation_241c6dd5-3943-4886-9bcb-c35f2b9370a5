<?php

use Illuminate\Database\Seeder;
use App\Country;
use App\User;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first user as creator
        $user = User::first();
        $userId = $user ? $user->id : null;

        $countries = [
            [
                'code' => 'IN',
                'name' => 'India',
                'serial_id' => 1,
                'phonecode' => '91',
                'timezone_id' => 'Asia/Kolkata',
                'currency_name' => 'Indian Rupee',
                'currency_code' => 'INR',
                'currency_symbol' => '₹',
                'is_active' => true,
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
            [
                'code' => 'US',
                'name' => 'United States',
                'serial_id' => 2,
                'phonecode' => '1',
                'timezone_id' => 'America/New_York',
                'currency_name' => 'US Dollar',
                'currency_code' => 'USD',
                'currency_symbol' => '$',
                'is_active' => true,
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
            [
                'code' => 'GB',
                'name' => 'United Kingdom',
                'serial_id' => 3,
                'phonecode' => '44',
                'timezone_id' => 'Europe/London',
                'currency_name' => 'British Pound',
                'currency_code' => 'GBP',
                'currency_symbol' => '£',
                'is_active' => true,
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
            [
                'code' => 'CA',
                'name' => 'Canada',
                'serial_id' => 4,
                'phonecode' => '1',
                'timezone_id' => 'America/Toronto',
                'currency_name' => 'Canadian Dollar',
                'currency_code' => 'CAD',
                'currency_symbol' => 'C$',
                'is_active' => true,
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
            [
                'code' => 'AU',
                'name' => 'Australia',
                'serial_id' => 5,
                'phonecode' => '61',
                'timezone_id' => 'Australia/Sydney',
                'currency_name' => 'Australian Dollar',
                'currency_code' => 'AUD',
                'currency_symbol' => 'A$',
                'is_active' => true,
                'created_by' => $userId,
                'updated_by' => $userId,
            ],
        ];

        foreach ($countries as $country) {
            Country::create($country);
        }
    }
}
